<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI侧边栏助手</title>
  <link rel="stylesheet" href="aiSidebarStyles.css">
</head>
<body>
  <!-- #region 主容器 -->
  <div id="ai-sidebar-container" class="ai-sidebar">
    
    <!-- #region 顶部导航栏 -->
    <header class="ai-sidebar__header">
      <div class="ai-sidebar__logo">
        <div class="ai-sidebar__logo-icon">🤖</div>
        <span class="ai-sidebar__logo-text">AI助手</span>
        <div class="ai-sidebar__status-dot" id="ai-connection-status"></div>
      </div>
      <div class="ai-sidebar__header-actions">
        <button id="ai-sidebar-notifications-btn" class="ai-sidebar__action-btn" title="通知">
          <span class="ai-icon">🔔</span>
          <span class="ai-badge hidden" id="ai-notification-badge">0</span>
        </button>
        <button id="ai-sidebar-settings-btn" class="ai-sidebar__action-btn" title="设置">
          <span class="ai-icon">⚙️</span>
        </button>
      </div>
    </header>
    <!-- #endregion -->
    
    <!-- #region 主标签页导航 -->
    <nav class="ai-sidebar__main-tabs">
      <button id="ai-main-tab-chat" class="ai-sidebar__main-tab ai-sidebar__main-tab--active" data-tab="chat">
        <span class="ai-icon">💬</span>
        <span class="ai-tab-text">对话</span>
      </button>
      <button id="ai-main-tab-analysis" class="ai-sidebar__main-tab" data-tab="analysis">
        <span class="ai-icon">📊</span>
        <span class="ai-tab-text">分析</span>
      </button>
      <button id="ai-main-tab-notion" class="ai-sidebar__main-tab" data-tab="notion">
        <span class="ai-icon">📝</span>
        <span class="ai-tab-text">Notion</span>
      </button>
      <button id="ai-main-tab-enhance" class="ai-sidebar__main-tab" data-tab="enhance">
        <span class="ai-icon">✨</span>
        <span class="ai-tab-text">增强</span>
      </button>
      <button id="ai-main-tab-settings" class="ai-sidebar__main-tab" data-tab="settings">
        <span class="ai-icon">⚙️</span>
        <span class="ai-tab-text">设置</span>
      </button>
    </nav>
    <!-- #endregion -->
    
    <!-- #region 主内容区域 -->
    <main class="ai-sidebar__main">
      
      <!-- #region 对话面板 -->
      <div id="ai-panel-chat" class="ai-sidebar__panel ai-sidebar__panel--active">
        
        <!-- 快速操作工具栏 -->
        <div class="ai-toolbar">
          <button id="ai-analyze-page-btn" class="ai-toolbar__btn ai-toolbar__btn--primary">
            <span class="ai-icon">🔍</span>
            <span>分析页面</span>
          </button>
          <button id="ai-smart-reply-btn" class="ai-toolbar__btn">
            <span class="ai-icon">🧠</span>
            <span>智能回复</span>
          </button>
          <button id="ai-template-btn" class="ai-toolbar__btn">
            <span class="ai-icon">📋</span>
            <span>模板</span>
          </button>
        </div>
        
        <!-- 对话容器 -->
        <div class="ai-chat">
          <div id="ai-chat-messages" class="ai-chat__messages">
            <!-- 欢迎消息 -->
            <div class="ai-chat__message ai-chat__message--assistant">
              <div class="ai-chat__avatar">
                <span class="ai-icon">🤖</span>
              </div>
              <div class="ai-chat__message-content">
                <div class="ai-chat__message-text">
                  你好！我是你的AI助手，已集成Notion同步、智能分析、光标增强等功能。请告诉我需要什么帮助？
                </div>
                <div class="ai-chat__message-time">刚刚</div>
              </div>
            </div>
          </div>
          
          <!-- 输入区域 -->
          <div class="ai-chat__input-area">
            <div class="ai-chat__input-container">
              <div class="ai-chat__input-wrapper">
                <textarea 
                  id="ai-chat-input" 
                  class="ai-chat__input" 
                  placeholder="输入你的问题或需求..."
                  rows="1"
                ></textarea>
                <div class="ai-chat__input-actions">
                  <button id="ai-chat-attach-btn" class="ai-chat__action-btn" title="附件">
                    <span class="ai-icon">📎</span>
                  </button>
                  <button id="ai-chat-emoji-btn" class="ai-chat__action-btn" title="表情">
                    <span class="ai-icon">😊</span>
                  </button>
                </div>
              </div>
              <button id="ai-chat-send-btn" class="ai-chat__send-btn">
                <span class="ai-icon">📤</span>
              </button>
            </div>
            <div class="ai-chat__suggestions hidden" id="ai-chat-suggestions">
              <!-- 智能建议将动态加载 -->
            </div>
          </div>
        </div>
      </div>
      <!-- #endregion -->
      
      <!-- #region 分析面板 -->
      <div id="ai-panel-analysis" class="ai-sidebar__panel">
        
        <!-- 分析工具栏 -->
        <div class="ai-toolbar">
          <button id="ai-analysis-start-btn" class="ai-toolbar__btn ai-toolbar__btn--primary">
            <span class="ai-icon">🚀</span>
            <span>开始分析</span>
          </button>
          <button id="ai-analysis-compare-btn" class="ai-toolbar__btn">
            <span class="ai-icon">⚖️</span>
            <span>对比分析</span>
          </button>
          <button id="ai-analysis-export-btn" class="ai-toolbar__btn">
            <span class="ai-icon">📤</span>
            <span>导出</span>
          </button>
        </div>
        
        <!-- 分析内容 -->
        <div class="ai-analysis">
          
          <!-- 分析标签页 -->
          <div class="ai-analysis__tabs">
            <button class="ai-analysis__tab ai-analysis__tab--active" data-analysis-tab="current">当前页面</button>
            <button class="ai-analysis__tab" data-analysis-tab="compare">对比分析</button>
            <button class="ai-analysis__tab" data-analysis-tab="trend">趋势分析</button>
            <button class="ai-analysis__tab" data-analysis-tab="history">历史记录</button>
          </div>
          
          <!-- 当前页面分析 -->
          <div class="ai-analysis__content ai-analysis__content--active" data-analysis-content="current">
            
            <!-- 分析控制面板 -->
            <div class="ai-analysis__controls">
              <div class="ai-analysis__options">
                <label class="ai-checkbox">
                  <input type="checkbox" id="ai-analysis-text" checked>
                  <span class="ai-checkbox__mark"></span>
                  <span class="ai-checkbox__label">文本内容</span>
                </label>
                <label class="ai-checkbox">
                  <input type="checkbox" id="ai-analysis-images" checked>
                  <span class="ai-checkbox__mark"></span>
                  <span class="ai-checkbox__label">图片信息</span>
                </label>
                <label class="ai-checkbox">
                  <input type="checkbox" id="ai-analysis-links">
                  <span class="ai-checkbox__mark"></span>
                  <span class="ai-checkbox__label">链接结构</span>
                </label>
                <label class="ai-checkbox">
                  <input type="checkbox" id="ai-analysis-seo">
                  <span class="ai-checkbox__mark"></span>
                  <span class="ai-checkbox__label">SEO优化</span>
                </label>
              </div>
              
              <div class="ai-analysis__depth">
                <label class="ai-setting-item__label">分析深度</label>
                <select class="ai-select ai-select--small" id="ai-analysis-depth" title="选择分析深度" aria-label="选择分析深度">
                  <option value="basic">基础分析</option>
                  <option value="standard" selected>标准分析</option>
                  <option value="detailed">详细分析</option>
                  <option value="comprehensive">全面分析</option>
                </select>
              </div>
            </div>
            
            <!-- 页面信息卡片 -->
            <div class="ai-page-info hidden" id="ai-page-info">
              <div class="ai-page-info__header">
                <div class="ai-page-info__icon">🌐</div>
                <div class="ai-page-info__details">
                  <h4 class="ai-page-info__title" id="ai-page-title">页面标题</h4>
                  <div class="ai-page-info__url" id="ai-page-url">https://example.com</div>
                  <div class="ai-page-info__meta">
                    <span class="ai-meta-item" id="ai-page-word-count">0 字</span>
                    <span class="ai-meta-item" id="ai-page-reading-time">0 分钟阅读</span>
                    <span class="ai-meta-item" id="ai-page-last-updated">刚刚更新</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 分析结果 -->
            <div id="ai-analysis-results" class="ai-analysis__results">
              <div class="ai-analysis__placeholder">
                <div class="ai-placeholder">
                  <span class="ai-placeholder__icon">📊</span>
                  <h3 class="ai-placeholder__title">页面分析</h3>
                  <p class="ai-placeholder__text">点击"开始分析"来分析当前页面内容，获取详细的页面信息和智能建议。</p>
                </div>
              </div>
            </div>
            
            <!-- 分析结果模板 -->
            <div id="ai-analysis-template" class="hidden">
              <div class="ai-analysis-result">
                
                <!-- 内容摘要 -->
                <div class="ai-result-section">
                  <div class="ai-result-section__header">
                    <h4 class="ai-result-section__title">
                      <span class="ai-result-section__icon">📝</span>
                      内容摘要
                    </h4>
                    <div class="ai-result-section__score">
                      <span class="ai-score" id="ai-score-content">85</span>
                    </div>
                  </div>
                  <div class="ai-result-section__content">
                    <div class="ai-summary" id="ai-content-summary">
                      页面内容摘要将在这里显示...
                    </div>
                    <div class="ai-keywords" id="ai-content-keywords">
                      <div class="ai-keywords__title">关键词</div>
                      <div class="ai-keywords__list">
                        <!-- 关键词标签将动态生成 -->
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 结构分析 -->
                <div class="ai-result-section">
                  <div class="ai-result-section__header">
                    <h4 class="ai-result-section__title">
                      <span class="ai-result-section__icon">🏗️</span>
                      页面结构
                    </h4>
                    <div class="ai-result-section__score">
                      <span class="ai-score" id="ai-score-structure">78</span>
                    </div>
                  </div>
                  <div class="ai-result-section__content">
                    <div class="ai-structure-metrics">
                      <div class="ai-metric-item">
                        <span class="ai-metric-label">标题层级</span>
                        <span class="ai-metric-value" id="ai-metric-headings">H1(1) H2(3) H3(5)</span>
                      </div>
                      <div class="ai-metric-item">
                        <span class="ai-metric-label">段落数量</span>
                        <span class="ai-metric-value" id="ai-metric-paragraphs">12</span>
                      </div>
                      <div class="ai-metric-item">
                        <span class="ai-metric-label">图片数量</span>
                        <span class="ai-metric-value" id="ai-metric-images">8</span>
                      </div>
                      <div class="ai-metric-item">
                        <span class="ai-metric-label">链接数量</span>
                        <span class="ai-metric-value" id="ai-metric-links">15</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- SEO分析 -->
                <div class="ai-result-section">
                  <div class="ai-result-section__header">
                    <h4 class="ai-result-section__title">
                      <span class="ai-result-section__icon">🔍</span>
                      SEO优化
                    </h4>
                    <div class="ai-result-section__score">
                      <span class="ai-score" id="ai-score-seo">92</span>
                    </div>
                  </div>
                  <div class="ai-result-section__content">
                    <div class="ai-seo-items" id="ai-seo-items">
                      <!-- SEO检查项将动态生成 -->
                    </div>
                  </div>
                </div>
                
                <!-- 智能建议 -->
                <div class="ai-result-section">
                  <div class="ai-result-section__header">
                    <h4 class="ai-result-section__title">
                      <span class="ai-result-section__icon">💡</span>
                      智能建议
                    </h4>
                  </div>
                  <div class="ai-result-section__content">
                    <div class="ai-suggestions" id="ai-suggestions">
                      <!-- 建议列表将动态生成 -->
                    </div>
                  </div>
                </div>
                
              </div>
            </div>
          </div>
          
          <!-- 对比分析 -->
          <div class="ai-analysis__content" data-analysis-content="compare">
            <div class="ai-compare">
                             <div class="ai-compare__selector">
                 <select id="ai-compare-pages" class="ai-select" title="选择要对比的页面" aria-label="选择要对比的页面">
                   <option value="">选择要对比的页面...</option>
                 </select>
                 <button id="ai-compare-start-btn" class="ai-btn ai-btn--small">开始对比</button>
               </div>
              <div id="ai-compare-results" class="ai-compare__results">
                <div class="ai-placeholder">
                  <span class="ai-placeholder__icon">⚖️</span>
                  <h3 class="ai-placeholder__title">多页面对比</h3>
                  <p class="ai-placeholder__text">选择页面进行智能对比分析，发现内容差异和相似之处。</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 趋势分析 -->
          <div class="ai-analysis__content" data-analysis-content="trend">
            <div class="ai-trend">
                             <div class="ai-trend__controls">
                 <select id="ai-trend-period" class="ai-select" title="选择趋势分析时间段" aria-label="选择趋势分析时间段">
                   <option value="7d">最近7天</option>
                   <option value="30d">最近30天</option>
                   <option value="90d">最近90天</option>
                 </select>
                 <button id="ai-trend-refresh-btn" class="ai-btn ai-btn--small">刷新</button>
               </div>
              <div id="ai-trend-chart" class="ai-trend__chart">
                <div class="ai-placeholder">
                  <span class="ai-placeholder__icon">📈</span>
                  <h3 class="ai-placeholder__title">趋势分析</h3>
                  <p class="ai-placeholder__text">查看页面访问和分析的趋势图表。</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 历史记录 -->
          <div class="ai-analysis__content" data-analysis-content="history">
            <div class="ai-history">
              <div class="ai-history__header">
                <h3>分析历史</h3>
                <button id="ai-history-clear-btn" class="ai-btn ai-btn--small ai-btn--danger">清空</button>
              </div>
              <div id="ai-history-list" class="ai-history__list">
                <div class="ai-placeholder">
                  <span class="ai-placeholder__icon">📋</span>
                  <h3 class="ai-placeholder__title">分析历史</h3>
                  <p class="ai-placeholder__text">这里会显示您的页面分析历史记录。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- #endregion -->
      
      <!-- #region Notion面板 -->
      <div id="ai-panel-notion" class="ai-sidebar__panel">
        
        <!-- Notion工具栏 -->
        <div class="ai-toolbar">
          <button id="ai-notion-connect-btn" class="ai-toolbar__btn ai-toolbar__btn--primary">
            <span class="ai-icon">🔗</span>
            <span>连接Notion</span>
          </button>
          <button id="ai-notion-sync-btn" class="ai-toolbar__btn" disabled>
            <span class="ai-icon">🔄</span>
            <span>同步</span>
          </button>
          <button id="ai-notion-settings-btn" class="ai-toolbar__btn">
            <span class="ai-icon">⚙️</span>
            <span>设置</span>
          </button>
        </div>
        
        <!-- Notion内容 -->
        <div class="ai-notion">
          
          <!-- 连接状态 -->
          <div class="ai-notion__status">
            <div class="ai-status-card" id="ai-notion-status-card">
              <div class="ai-status-card__icon">
                <span class="ai-icon">❌</span>
              </div>
              <div class="ai-status-card__content">
                <h4 class="ai-status-card__title">未连接</h4>
                <p class="ai-status-card__text">点击"连接Notion"开始使用云端同步功能</p>
              </div>
            </div>
          </div>
          
          <!-- Notion标签页 -->
          <div class="ai-notion__tabs">
            <button class="ai-notion__tab ai-notion__tab--active" data-notion-tab="sync">同步管理</button>
            <button class="ai-notion__tab" data-notion-tab="search">知识库搜索</button>
            <button class="ai-notion__tab" data-notion-tab="databases">数据库</button>
          </div>
          
          <!-- 同步管理 -->
          <div class="ai-notion__content ai-notion__content--active" data-notion-content="sync">
            <div class="ai-sync">
              <div class="ai-sync__stats">
                <div class="ai-stat-item">
                  <span class="ai-stat-item__label">聊天记录</span>
                  <span class="ai-stat-item__value" id="ai-sync-chat-count">0</span>
                </div>
                <div class="ai-stat-item">
                  <span class="ai-stat-item__label">分析结果</span>
                  <span class="ai-stat-item__value" id="ai-sync-analysis-count">0</span>
                </div>
                <div class="ai-stat-item">
                  <span class="ai-stat-item__label">最后同步</span>
                  <span class="ai-stat-item__value" id="ai-sync-last-time">从未</span>
                </div>
              </div>
              
              <div class="ai-sync__controls">
                <div class="ai-checkbox-group">
                  <label class="ai-checkbox">
                    <input type="checkbox" id="ai-sync-auto" checked>
                    <span class="ai-checkbox__mark"></span>
                    <span class="ai-checkbox__label">自动同步</span>
                  </label>
                  <label class="ai-checkbox">
                    <input type="checkbox" id="ai-sync-chat" checked>
                    <span class="ai-checkbox__mark"></span>
                    <span class="ai-checkbox__label">同步聊天记录</span>
                  </label>
                  <label class="ai-checkbox">
                    <input type="checkbox" id="ai-sync-analysis" checked>
                    <span class="ai-checkbox__mark"></span>
                    <span class="ai-checkbox__label">同步分析结果</span>
                  </label>
                </div>
              </div>
              
              <div id="ai-sync-progress" class="ai-progress hidden">
                <div class="ai-progress__bar">
                  <div class="ai-progress__fill" style="width: 0%"></div>
                </div>
                <div class="ai-progress__text">同步中...</div>
              </div>
            </div>
          </div>
          
          <!-- 知识库搜索 -->
          <div class="ai-notion__content" data-notion-content="search">
            <div class="ai-search">
              <div class="ai-search__input-group">
                <input type="text" id="ai-notion-search-input" class="ai-search__input" placeholder="搜索知识库...">
                <button id="ai-notion-search-btn" class="ai-search__btn">
                  <span class="ai-icon">🔍</span>
                </button>
              </div>
              <div id="ai-notion-search-results" class="ai-search__results">
                <div class="ai-placeholder">
                  <span class="ai-placeholder__icon">🔍</span>
                  <h3 class="ai-placeholder__title">知识库搜索</h3>
                  <p class="ai-placeholder__text">在您的Notion知识库中搜索相关内容。</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 数据库管理 -->
          <div class="ai-notion__content" data-notion-content="databases">
            <div class="ai-databases">
              <div class="ai-databases__header">
                <h3>数据库管理</h3>
                <button id="ai-databases-refresh-btn" class="ai-btn ai-btn--small">刷新</button>
              </div>
              <div id="ai-databases-list" class="ai-databases__list">
                <div class="ai-placeholder">
                  <span class="ai-placeholder__icon">🗄️</span>
                  <h3 class="ai-placeholder__title">Notion数据库</h3>
                  <p class="ai-placeholder__text">连接Notion后查看和管理您的数据库。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- #endregion -->
      
      <!-- #region 增强面板 -->
      <div id="ai-panel-enhance" class="ai-sidebar__panel">
        
        <!-- 增强工具栏 -->
        <div class="ai-toolbar">
          <button id="ai-enhance-toggle-btn" class="ai-toolbar__btn ai-toolbar__btn--primary">
            <span class="ai-icon">✨</span>
            <span>启用增强</span>
          </button>
          <button id="ai-enhance-train-btn" class="ai-toolbar__btn">
            <span class="ai-icon">🧠</span>
            <span>训练模式</span>
          </button>
          <button id="ai-enhance-stats-btn" class="ai-toolbar__btn">
            <span class="ai-icon">📊</span>
            <span>统计</span>
          </button>
        </div>
        
        <!-- 增强内容 -->
        <div class="ai-enhance">
          
          <!-- 增强标签页 -->
          <div class="ai-enhance__tabs">
            <button class="ai-enhance__tab ai-enhance__tab--active" data-enhance-tab="cursor">光标增强</button>
            <button class="ai-enhance__tab" data-enhance-tab="shortcuts">快捷键</button>
            <button class="ai-enhance__tab" data-enhance-tab="templates">模板库</button>
            <button class="ai-enhance__tab" data-enhance-tab="stats">使用统计</button>
          </div>
          
          <!-- 光标增强 -->
          <div class="ai-enhance__content ai-enhance__content--active" data-enhance-content="cursor">
            <div class="ai-cursor-enhance">
              
              <!-- 状态显示 -->
              <div class="ai-status-card" id="ai-cursor-status-card">
                <div class="ai-status-card__icon">
                  <span class="ai-icon">❌</span>
                </div>
                <div class="ai-status-card__content">
                  <h4 class="ai-status-card__title">光标增强已禁用</h4>
                  <p class="ai-status-card__text">启用后可获得智能输入预测和自动补全功能</p>
                </div>
              </div>
              
              <!-- 设置选项 -->
              <div class="ai-settings-group">
                <h4 class="ai-settings-group__title">预测设置</h4>
                <div class="ai-setting-item">
                  <label class="ai-setting-item__label">
                    <span>触发延迟</span>
                    <input type="range" id="ai-cursor-delay" class="ai-range" min="100" max="2000" value="500" step="100">
                    <span class="ai-setting-item__value">500ms</span>
                  </label>
                </div>
                <div class="ai-setting-item">
                  <label class="ai-setting-item__label">
                    <span>建议数量</span>
                    <input type="range" id="ai-cursor-suggestions" class="ai-range" min="1" max="10" value="3" step="1">
                    <span class="ai-setting-item__value">3</span>
                  </label>
                </div>
                <div class="ai-setting-item">
                  <label class="ai-checkbox">
                    <input type="checkbox" id="ai-cursor-auto-complete" checked>
                    <span class="ai-checkbox__mark"></span>
                    <span class="ai-checkbox__label">自动补全</span>
                  </label>
                </div>
                <div class="ai-setting-item">
                  <label class="ai-checkbox">
                    <input type="checkbox" id="ai-cursor-context-aware" checked>
                    <span class="ai-checkbox__mark"></span>
                    <span class="ai-checkbox__label">上下文感知</span>
                  </label>
                </div>
              </div>
              
              <!-- 支持的元素类型 -->
              <div class="ai-settings-group">
                <h4 class="ai-settings-group__title">支持的输入类型</h4>
                <div class="ai-checkbox-group">
                  <label class="ai-checkbox">
                    <input type="checkbox" id="ai-cursor-input" checked>
                    <span class="ai-checkbox__mark"></span>
                    <span class="ai-checkbox__label">文本输入框</span>
                  </label>
                  <label class="ai-checkbox">
                    <input type="checkbox" id="ai-cursor-textarea" checked>
                    <span class="ai-checkbox__mark"></span>
                    <span class="ai-checkbox__label">多行文本框</span>
                  </label>
                  <label class="ai-checkbox">
                    <input type="checkbox" id="ai-cursor-contenteditable" checked>
                    <span class="ai-checkbox__mark"></span>
                    <span class="ai-checkbox__label">富文本编辑器</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 快捷键 -->
          <div class="ai-enhance__content" data-enhance-content="shortcuts">
            <div class="ai-shortcuts">
              <h4 class="ai-shortcuts__title">快捷键设置</h4>
              <div class="ai-shortcut-list">
                <div class="ai-shortcut-item">
                  <span class="ai-shortcut-item__label">接受建议</span>
                  <kbd class="ai-kbd">Tab</kbd>
                </div>
                <div class="ai-shortcut-item">
                  <span class="ai-shortcut-item__label">取消建议</span>
                  <kbd class="ai-kbd">Esc</kbd>
                </div>
                <div class="ai-shortcut-item">
                  <span class="ai-shortcut-item__label">手动触发</span>
                  <kbd class="ai-kbd">Ctrl</kbd> + <kbd class="ai-kbd">Space</kbd>
                </div>
                <div class="ai-shortcut-item">
                  <span class="ai-shortcut-item__label">上一个建议</span>
                  <kbd class="ai-kbd">↑</kbd>
                </div>
                <div class="ai-shortcut-item">
                  <span class="ai-shortcut-item__label">下一个建议</span>
                  <kbd class="ai-kbd">↓</kbd>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 模板库 -->
          <div class="ai-enhance__content" data-enhance-content="templates">
            <div class="ai-templates">
              <div class="ai-templates__header">
                <h4>智能模板</h4>
                <div class="ai-templates__controls">
                  <select class="ai-select ai-select--small" id="ai-templates-category" title="选择模板分类" aria-label="选择模板分类">
                    <option value="all">全部分类</option>
                    <option value="email">邮件模板</option>
                    <option value="response">客服回复</option>
                    <option value="report">报告模板</option>
                    <option value="social">社交媒体</option>
                    <option value="custom">自定义</option>
                  </select>
                  <button id="ai-templates-add-btn" class="ai-btn ai-btn--small ai-btn--primary">
                    <span class="ai-icon">➕</span>
                    <span>添加模板</span>
                  </button>
                </div>
              </div>
              
              <!-- 模板搜索 -->
              <div class="ai-templates__search">
                <div class="ai-search__input-group">
                  <input type="text" id="ai-templates-search" class="ai-search__input" placeholder="搜索模板...">
                  <button class="ai-search__btn">
                    <span class="ai-icon">🔍</span>
                  </button>
                </div>
              </div>
              
              <!-- 预设模板 -->
              <div class="ai-templates__presets" id="ai-templates-presets">
                <div class="ai-template-card" data-template-id="email-follow-up" data-category="email">
                  <div class="ai-template-card__header">
                    <div class="ai-template-card__icon">📧</div>
                    <div class="ai-template-card__info">
                      <h5 class="ai-template-card__title">邮件跟进</h5>
                      <div class="ai-template-card__category">邮件模板</div>
                    </div>
                    <div class="ai-template-card__actions">
                      <button class="ai-template-card__btn" title="使用模板">
                        <span class="ai-icon">✨</span>
                      </button>
                      <button class="ai-template-card__btn" title="编辑模板">
                        <span class="ai-icon">✏️</span>
                      </button>
                    </div>
                  </div>
                  <div class="ai-template-card__preview">
                    尊敬的{客户姓名}，感谢您对我们产品的关注。针对您的询问...
                  </div>
                  <div class="ai-template-card__meta">
                    <span class="ai-template-meta__item">使用 23 次</span>
                    <span class="ai-template-meta__item">2天前更新</span>
                  </div>
                </div>
                
                <div class="ai-template-card" data-template-id="customer-service" data-category="customer-service">
                  <div class="ai-template-card__header">
                    <div class="ai-template-card__icon">🎧</div>
                    <div class="ai-template-card__info">
                      <h5 class="ai-template-card__title">客服回复</h5>
                      <div class="ai-template-card__category">客服回复</div>
                    </div>
                    <div class="ai-template-card__actions">
                      <button class="ai-template-card__btn" title="使用模板">
                        <span class="ai-icon">✨</span>
                      </button>
                      <button class="ai-template-card__btn" title="编辑模板">
                        <span class="ai-icon">✏️</span>
                      </button>
                    </div>
                  </div>
                  <div class="ai-template-card__preview">
                    您好！感谢您联系我们的客服团队。我已经收到您关于{问题类型}的咨询...
                  </div>
                  <div class="ai-template-card__meta">
                    <span class="ai-template-meta__item">使用 45 次</span>
                    <span class="ai-template-meta__item">1周前更新</span>
                  </div>
                </div>
                
                <div class="ai-template-card" data-template-id="meeting-summary" data-category="report">
                  <div class="ai-template-card__header">
                    <div class="ai-template-card__icon">📊</div>
                    <div class="ai-template-card__info">
                      <h5 class="ai-template-card__title">会议纪要</h5>
                      <div class="ai-template-card__category">报告模板</div>
                    </div>
                    <div class="ai-template-card__actions">
                      <button class="ai-template-card__btn" title="使用模板">
                        <span class="ai-icon">✨</span>
                      </button>
                      <button class="ai-template-card__btn" title="编辑模板">
                        <span class="ai-icon">✏️</span>
                      </button>
                    </div>
                  </div>
                  <div class="ai-template-card__preview">
                    会议时间：{会议时间}
                    参会人员：{参会人员}
                    会议主题：{会议主题}
                    主要讨论内容：...
                  </div>
                  <div class="ai-template-card__meta">
                    <span class="ai-template-meta__item">使用 12 次</span>
                    <span class="ai-template-meta__item">3天前更新</span>
                  </div>
                </div>
              </div>
              
              <!-- 自定义模板列表 -->
              <div id="ai-templates-list" class="ai-templates__list">
                <div class="ai-placeholder" id="ai-templates-placeholder">
                  <span class="ai-placeholder__icon">📋</span>
                  <h3 class="ai-placeholder__title">暂无自定义模板</h3>
                  <p class="ai-placeholder__text">点击"添加模板"创建您的第一个自定义模板。</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 使用统计 -->
          <div class="ai-enhance__content" data-enhance-content="stats">
            <div class="ai-enhance-stats">
              <div class="ai-stats-grid">
                <div class="ai-stat-card">
                  <div class="ai-stat-card__icon">💡</div>
                  <div class="ai-stat-card__content">
                    <div class="ai-stat-card__value" id="ai-stats-suggestions">0</div>
                    <div class="ai-stat-card__label">建议次数</div>
                  </div>
                </div>
                <div class="ai-stat-card">
                  <div class="ai-stat-card__icon">✅</div>
                  <div class="ai-stat-card__content">
                    <div class="ai-stat-card__value" id="ai-stats-accepted">0</div>
                    <div class="ai-stat-card__label">接受次数</div>
                  </div>
                </div>
                <div class="ai-stat-card">
                  <div class="ai-stat-card__icon">📈</div>
                  <div class="ai-stat-card__content">
                    <div class="ai-stat-card__value" id="ai-stats-accuracy">0%</div>
                    <div class="ai-stat-card__label">准确率</div>
                  </div>
                </div>
                <div class="ai-stat-card">
                  <div class="ai-stat-card__icon">⚡</div>
                  <div class="ai-stat-card__content">
                    <div class="ai-stat-card__value" id="ai-stats-time-saved">0s</div>
                    <div class="ai-stat-card__label">节省时间</div>
                  </div>
                </div>
              </div>
              
              <div class="ai-stats-chart">
                <h4>使用趋势</h4>
                <div id="ai-stats-chart-container" class="ai-chart-container">
                  <div class="ai-placeholder">
                    <span class="ai-placeholder__icon">📊</span>
                    <p class="ai-placeholder__text">使用一段时间后这里会显示详细的统计图表。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- #endregion -->

      <!-- #region 设置面板 -->
      <div id="ai-panel-settings" class="ai-sidebar__panel">

        <!-- 设置工具栏 -->
        <div class="ai-toolbar">
          <button id="ai-settings-refresh-btn" class="ai-toolbar__btn">
            <span class="ai-icon">🔄</span>
            <span class="ai-btn-text">刷新设置</span>
          </button>
          <button id="ai-settings-export-btn" class="ai-toolbar__btn">
            <span class="ai-icon">📤</span>
            <span class="ai-btn-text">导出设置</span>
          </button>
          <button id="ai-settings-import-btn" class="ai-toolbar__btn">
            <span class="ai-icon">📥</span>
            <span class="ai-btn-text">导入设置</span>
          </button>
        </div>

        <!-- 设置内容 -->
        <div class="ai-settings__content">

          <!-- 快速设置 -->
          <div class="ai-settings__section">
            <h3 class="ai-settings__section-title">快速设置</h3>
            <div class="ai-settings__grid">

              <div class="ai-setting-card">
                <div class="ai-setting-card__header">
                  <div class="ai-setting-card__icon">🎨</div>
                  <div class="ai-setting-card__info">
                    <h4 class="ai-setting-card__title">主题模式</h4>
                    <p class="ai-setting-card__desc">选择界面主题</p>
                  </div>
                </div>
                <div class="ai-setting-card__control">
                  <select id="ai-setting-theme" class="ai-select" title="选择主题模式" aria-label="主题模式选择">
                    <option value="auto">跟随系统</option>
                    <option value="light">浅色模式</option>
                    <option value="dark">深色模式</option>
                  </select>
                </div>
              </div>

              <div class="ai-setting-card">
                <div class="ai-setting-card__header">
                  <div class="ai-setting-card__icon">🔔</div>
                  <div class="ai-setting-card__info">
                    <h4 class="ai-setting-card__title">通知设置</h4>
                    <p class="ai-setting-card__desc">管理通知提醒</p>
                  </div>
                </div>
                <div class="ai-setting-card__control">
                  <div class="ai-switch">
                    <input type="checkbox" id="ai-setting-notifications" checked title="启用或禁用通知" aria-label="通知设置开关">
                    <span class="ai-switch__slider"></span>
                  </div>
                </div>
              </div>

              <div class="ai-setting-card">
                <div class="ai-setting-card__header">
                  <div class="ai-setting-card__icon">🚀</div>
                  <div class="ai-setting-card__info">
                    <h4 class="ai-setting-card__title">自动启动</h4>
                    <p class="ai-setting-card__desc">页面加载时自动打开</p>
                  </div>
                </div>
                <div class="ai-setting-card__control">
                  <div class="ai-switch">
                    <input type="checkbox" id="ai-setting-auto-start" title="页面加载时自动启动" aria-label="自动启动开关">
                    <span class="ai-switch__slider"></span>
                  </div>
                </div>
              </div>

              <div class="ai-setting-card">
                <div class="ai-setting-card__header">
                  <div class="ai-setting-card__icon">🔒</div>
                  <div class="ai-setting-card__info">
                    <h4 class="ai-setting-card__title">隐私模式</h4>
                    <p class="ai-setting-card__desc">增强隐私保护</p>
                  </div>
                </div>
                <div class="ai-setting-card__control">
                  <div class="ai-switch">
                    <input type="checkbox" id="ai-setting-privacy-mode" title="启用隐私模式" aria-label="隐私模式开关">
                    <span class="ai-switch__slider"></span>
                  </div>
                </div>
              </div>

            </div>
          </div>

          <!-- API配置 -->
          <div class="ai-settings__section">
            <h3 class="ai-settings__section-title">API配置</h3>
            <div class="ai-settings__form">

              <div class="ai-form-group">
                <label class="ai-form-label" for="ai-setting-gemini-key">Gemini API密钥</label>
                <div class="ai-input-group">
                  <input type="password" id="ai-setting-gemini-key" class="ai-input" placeholder="输入您的Gemini API密钥">
                  <button class="ai-input-group__btn" type="button" id="ai-gemini-key-toggle">
                    <span class="ai-icon">👁️</span>
                  </button>
                </div>
                <div class="ai-form-help">在Google AI Studio获取API密钥</div>
              </div>

              <div class="ai-form-group">
                <label class="ai-form-label" for="ai-setting-notion-token">Notion Integration Token</label>
                <div class="ai-input-group">
                  <input type="password" id="ai-setting-notion-token" class="ai-input" placeholder="输入您的Notion Integration Token">
                  <button class="ai-input-group__btn" type="button" id="ai-notion-token-toggle">
                    <span class="ai-icon">👁️</span>
                  </button>
                </div>
                <div class="ai-form-help">在Notion Integrations页面获取Token</div>
              </div>

            </div>
          </div>

          <!-- 系统信息 -->
          <div class="ai-settings__section">
            <h3 class="ai-settings__section-title">系统信息</h3>
            <div class="ai-system-info">

              <div class="ai-info-item">
                <span class="ai-info-label">扩展版本:</span>
                <span class="ai-info-value" id="ai-info-version">v2.0.0</span>
              </div>

              <div class="ai-info-item">
                <span class="ai-info-label">浏览器:</span>
                <span class="ai-info-value" id="ai-info-browser">Chrome</span>
              </div>

              <div class="ai-info-item">
                <span class="ai-info-label">存储使用:</span>
                <span class="ai-info-value" id="ai-info-storage">计算中...</span>
              </div>

              <div class="ai-info-item">
                <span class="ai-info-label">缓存状态:</span>
                <span class="ai-info-value" id="ai-info-cache">检查中...</span>
              </div>

            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="ai-settings__actions">
            <button class="ai-btn ai-btn--secondary" id="ai-settings-reset-all">重置所有设置</button>
            <button class="ai-btn ai-btn--secondary" id="ai-settings-clear-cache">清除缓存</button>
            <button class="ai-btn ai-btn--primary" id="ai-settings-save-all">保存设置</button>
          </div>

        </div>
      </div>
      <!-- #endregion -->

    </main>
    <!-- #endregion -->
    
    <!-- #region 状态栏 -->
    <footer class="ai-sidebar__footer">
      <div class="ai-sidebar__status-bar">
        <div id="ai-status-indicator" class="ai-status-indicator">
          <span class="ai-status-indicator__dot"></span>
          <span class="ai-status-indicator__text">准备就绪</span>
        </div>
        <div class="ai-sidebar__version">v2.0</div>
      </div>
    </footer>
    <!-- #endregion -->
    
  </div>
  <!-- #endregion -->
  
  <!-- #region 模态框和弹窗 -->
  
  <!-- 设置模态框 -->
  <div id="ai-settings-modal" class="ai-modal hidden">
    <div class="ai-modal__overlay"></div>
    <div class="ai-modal__content ai-modal__content--large">
      <div class="ai-modal__header">
        <h3 class="ai-modal__title">设置</h3>
        <button class="ai-modal__close-btn" id="ai-settings-modal-close">
          <span class="ai-icon">❌</span>
        </button>
      </div>
      <div class="ai-modal__body">
        <!-- 设置导航 -->
        <div class="ai-settings-nav">
          <button class="ai-settings-nav__item ai-settings-nav__item--active" data-settings-tab="features">功能开关</button>
          <button class="ai-settings-nav__item" data-settings-tab="ui">界面设置</button>
          <button class="ai-settings-nav__item" data-settings-tab="chat">对话设置</button>
          <button class="ai-settings-nav__item" data-settings-tab="analysis">分析设置</button>
          <button class="ai-settings-nav__item" data-settings-tab="templates">模板设置</button>
          <button class="ai-settings-nav__item" data-settings-tab="sync">同步设置</button>
          <button class="ai-settings-nav__item" data-settings-tab="privacy">隐私安全</button>
          <button class="ai-settings-nav__item" data-settings-tab="performance">性能设置</button>
          <button class="ai-settings-nav__item" data-settings-tab="shortcuts">快捷键</button>
          <button class="ai-settings-nav__item" data-settings-tab="notifications">通知设置</button>
        </div>
        
        <!-- 设置内容 -->
        <div class="ai-settings-content">
          <div id="ai-settings-content-container">
            
            <!-- 功能开关 -->
            <div class="ai-settings-panel ai-settings-panel--active" data-settings-panel="features">
              <div class="ai-settings__group">
                <div class="ai-settings__group-title">核心功能</div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-content-analysis">智能内容分析</label>
                    <div class="ai-settings__item-desc">自动分析网页内容并提供洞察</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-content-analysis" checked title="智能内容分析" aria-label="智能内容分析">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-cursor-enhancement">光标输入增强</label>
                    <div class="ai-settings__item-desc">智能预测和自动补全输入内容</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-cursor-enhancement" checked title="光标输入增强" aria-label="光标输入增强">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-notion-sync">Notion集成</label>
                    <div class="ai-settings__item-desc">同步对话历史和分析结果到Notion</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-notion-sync" title="Notion集成" aria-label="Notion集成">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-advanced-analysis">高级分析功能</label>
                    <div class="ai-settings__item-desc">多页面对比、趋势分析等高级功能</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-advanced-analysis" checked title="高级分析功能" aria-label="高级分析功能">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-template-system">模板管理系统</label>
                    <div class="ai-settings__item-desc">智能模板创建和管理功能</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-template-system" checked title="模板管理系统" aria-label="模板管理系统">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 界面设置 -->
            <div class="ai-settings-panel" data-settings-panel="ui">
              <div class="ai-settings__group">
                <div class="ai-settings__group-title">外观主题</div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-theme-modal">主题模式</label>
                    <div class="ai-settings__item-desc">选择浅色或深色主题</div>
                  </div>
                  <div class="ai-settings__control">
                    <select class="ai-select" id="ai-setting-theme-modal" title="主题模式" aria-label="主题模式">
                      <option value="auto">跟随系统</option>
                      <option value="light">浅色主题</option>
                      <option value="dark">深色主题</option>
                    </select>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-language">界面语言</label>
                    <div class="ai-settings__item-desc">选择界面显示语言</div>
                  </div>
                  <div class="ai-settings__control">
                    <select class="ai-select" id="ai-setting-language" title="界面语言" aria-label="界面语言">
                      <option value="zh-CN">简体中文</option>
                      <option value="en-US">English</option>
                      <option value="ja-JP">日本語</option>
                      <option value="ko-KR">한국어</option>
                    </select>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-sidebar-width">侧边栏宽度</label>
                    <div class="ai-settings__item-desc">调整侧边栏的默认宽度</div>
                  </div>
                  <div class="ai-settings__control">
                    <input type="range" class="ai-range" id="ai-setting-sidebar-width" min="300" max="800" value="400" title="侧边栏宽度" aria-label="侧边栏宽度">
                    <span class="ai-settings__range-value">400px</span>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-font-size">字体大小</label>
                    <div class="ai-settings__item-desc">调整界面字体大小</div>
                  </div>
                  <div class="ai-settings__control">
                    <select class="ai-select" id="ai-setting-font-size" title="字体大小" aria-label="字体大小">
                      <option value="small">小</option>
                      <option value="medium" selected>中</option>
                      <option value="large">大</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <!-- 对话设置 -->
            <div class="ai-settings-panel" data-settings-panel="chat">
              <div class="ai-settings__group">
                <div class="ai-settings__group-title">对话行为</div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-auto-send">自动发送消息</label>
                    <div class="ai-settings__item-desc">按Enter键自动发送消息</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-auto-send" checked title="自动发送消息" aria-label="自动发送消息">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-streaming">流式响应</label>
                    <div class="ai-settings__item-desc">实时显示AI回复内容</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-streaming" checked title="流式响应" aria-label="流式响应">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-save-history">对话历史保存</label>
                    <div class="ai-settings__item-desc">保存对话历史记录</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-save-history" checked title="对话历史保存" aria-label="对话历史保存">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-history-days">历史记录保留天数</label>
                    <div class="ai-settings__item-desc">自动清理超过指定天数的历史记录</div>
                  </div>
                  <div class="ai-settings__control">
                    <input type="number" class="ai-input ai-input--small" id="ai-setting-history-days" value="30" min="1" max="365" title="历史记录保留天数" aria-label="历史记录保留天数">
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-message-max-length">消息最大长度</label>
                    <div class="ai-settings__item-desc">单条消息的最大字符数</div>
                  </div>
                  <div class="ai-settings__control">
                    <input type="number" class="ai-input ai-input--small" id="ai-setting-message-max-length" value="4000" min="100" max="8000" title="消息最大长度" aria-label="消息最大长度">
                  </div>
                </div>
              </div>
            </div>

            <!-- 分析设置 -->
            <div class="ai-settings-panel" data-settings-panel="analysis">
              <div class="ai-settings__group">
                <div class="ai-settings__group-title">分析行为</div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-auto-analysis">自动分析页面</label>
                    <div class="ai-settings__item-desc">页面加载完成后自动进行内容分析</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-auto-analysis" title="自动分析页面" aria-label="自动分析页面">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-analysis-depth-modal">分析深度</label>
                    <div class="ai-settings__item-desc">选择内容分析的详细程度</div>
                  </div>
                  <div class="ai-settings__control">
                    <select class="ai-select" id="ai-setting-analysis-depth-modal" title="分析深度" aria-label="分析深度">
                      <option value="basic">基础分析</option>
                      <option value="standard" selected>标准分析</option>
                      <option value="detailed">详细分析</option>
                    </select>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-cache-analysis">缓存分析结果</label>
                    <div class="ai-settings__item-desc">缓存分析结果以提高性能</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-cache-analysis" checked title="缓存分析结果" aria-label="缓存分析结果">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-analysis-cache-hours">分析结果保留时间</label>
                    <div class="ai-settings__item-desc">分析结果的缓存时间（小时）</div>
                  </div>
                  <div class="ai-settings__control">
                    <input type="number" class="ai-input ai-input--small" id="ai-setting-analysis-cache-hours" value="24" min="1" max="168" title="分析结果保留时间" aria-label="分析结果保留时间">
                  </div>
                </div>
              </div>
            </div>

            <!-- 模板设置 -->
            <div class="ai-settings-panel" data-settings-panel="templates">
              <div class="ai-settings__group">
                <div class="ai-settings__group-title">模板管理</div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-template-language">默认模板语言</label>
                    <div class="ai-settings__item-desc">新建模板时的默认语言</div>
                  </div>
                  <div class="ai-settings__control">
                    <select class="ai-select" id="ai-setting-template-language" title="默认模板语言" aria-label="默认模板语言">
                      <option value="zh-CN">中文</option>
                      <option value="en-US">English</option>
                    </select>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-template-auto-save">自动保存模板</label>
                    <div class="ai-settings__item-desc">编辑模板时自动保存</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-template-auto-save" checked title="自动保存模板" aria-label="自动保存模板">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-template-categories">模板分类管理</label>
                    <div class="ai-settings__item-desc">启用模板分类功能</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-template-categories" checked title="模板分类管理" aria-label="模板分类管理">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 同步设置 -->
            <div class="ai-settings-panel" data-settings-panel="sync">
              <div class="ai-settings__group">
                <div class="ai-settings__group-title">Notion同步</div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-auto-sync">自动同步</label>
                    <div class="ai-settings__item-desc">自动同步数据到Notion</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-auto-sync" title="自动同步" aria-label="自动同步">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-sync-frequency">同步频率</label>
                    <div class="ai-settings__item-desc">自动同步的时间间隔</div>
                  </div>
                  <div class="ai-settings__control">
                    <select class="ai-select" id="ai-setting-sync-frequency" title="同步频率" aria-label="同步频率">
                      <option value="5">5分钟</option>
                      <option value="15" selected>15分钟</option>
                      <option value="30">30分钟</option>
                      <option value="60">1小时</option>
                    </select>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label">同步范围</label>
                    <div class="ai-settings__item-desc">选择要同步的数据类型</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-checkbox-group">
                      <label class="ai-checkbox">
                        <input type="checkbox" id="ai-setting-sync-chats" checked title="同步对话记录" aria-label="同步对话记录">
                        <span class="ai-checkbox__mark"></span>
                        <span class="ai-checkbox__label">对话记录</span>
                      </label>
                      <label class="ai-checkbox">
                        <input type="checkbox" id="ai-setting-sync-analysis" checked title="同步分析结果" aria-label="同步分析结果">
                        <span class="ai-checkbox__mark"></span>
                        <span class="ai-checkbox__label">分析结果</span>
                      </label>
                      <label class="ai-checkbox">
                        <input type="checkbox" id="ai-setting-sync-templates" title="同步模板数据" aria-label="同步模板数据">
                        <span class="ai-checkbox__mark"></span>
                        <span class="ai-checkbox__label">模板数据</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 隐私安全 -->
            <div class="ai-settings-panel" data-settings-panel="privacy">
              <div class="ai-settings__group">
                <div class="ai-settings__group-title">数据保护</div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-data-encryption">数据加密</label>
                    <div class="ai-settings__item-desc">对本地存储的敏感数据进行加密</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-data-encryption" checked title="数据加密" aria-label="数据加密">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-anonymous-stats">匿名使用统计</label>
                    <div class="ai-settings__item-desc">允许收集匿名使用统计以改进产品</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-anonymous-stats" title="匿名使用统计" aria-label="匿名使用统计">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-auto-cleanup">自动清理数据</label>
                    <div class="ai-settings__item-desc">定期清理临时和缓存数据</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-auto-cleanup" checked title="自动清理数据" aria-label="自动清理数据">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-local-api-storage">API密钥本地存储</label>
                    <div class="ai-settings__item-desc">将API密钥加密存储在本地</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-local-api-storage" checked title="API密钥本地存储" aria-label="API密钥本地存储">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 性能设置 -->
            <div class="ai-settings-panel" data-settings-panel="performance">
              <div class="ai-settings__group">
                <div class="ai-settings__group-title">性能优化</div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-memory-limit">内存限制</label>
                    <div class="ai-settings__item-desc">设置扩展的最大内存使用量</div>
                  </div>
                  <div class="ai-settings__control">
                    <input type="range" class="ai-range" id="ai-setting-memory-limit" min="20" max="100" value="40" title="内存限制" aria-label="内存限制">
                    <span class="ai-settings__range-value">40MB</span>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-cache-size">缓存大小</label>
                    <div class="ai-settings__item-desc">设置本地缓存的最大大小</div>
                  </div>
                  <div class="ai-settings__control">
                    <input type="range" class="ai-range" id="ai-setting-cache-size" min="10" max="100" value="50" title="缓存大小" aria-label="缓存大小">
                    <span class="ai-settings__range-value">50MB</span>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-preload-modules">预加载模块</label>
                    <div class="ai-settings__item-desc">启动时预加载常用模块</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-preload-modules" checked title="预加载模块" aria-label="预加载模块">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-gpu-acceleration">GPU加速</label>
                    <div class="ai-settings__item-desc">启用GPU硬件加速（如果支持）</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-gpu-acceleration" title="GPU加速" aria-label="GPU加速">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 快捷键设置 -->
            <div class="ai-settings-panel" data-settings-panel="shortcuts">
              <div class="ai-settings__group">
                <div class="ai-settings__group-title">全局快捷键</div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-shortcut-toggle">打开/关闭侧边栏</label>
                    <div class="ai-settings__item-desc">快速显示或隐藏AI侧边栏</div>
                  </div>
                  <div class="ai-settings__control">
                    <input type="text" class="ai-input" id="ai-setting-shortcut-toggle" value="Ctrl+Shift+A" readonly title="打开/关闭侧边栏快捷键" aria-label="打开/关闭侧边栏快捷键">
                    <button class="ai-btn ai-btn--small" onclick="this.previousElementSibling.readOnly = false">编辑</button>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-shortcut-analyze">快速分析页面</label>
                    <div class="ai-settings__item-desc">立即分析当前页面内容</div>
                  </div>
                  <div class="ai-settings__control">
                    <input type="text" class="ai-input" id="ai-setting-shortcut-analyze" value="Ctrl+Shift+E" readonly title="快速分析页面快捷键" aria-label="快速分析页面快捷键">
                    <button class="ai-btn ai-btn--small" onclick="this.previousElementSibling.readOnly = false">编辑</button>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-shortcut-reply">智能回复建议</label>
                    <div class="ai-settings__item-desc">在输入框中触发智能回复建议</div>
                  </div>
                  <div class="ai-settings__control">
                    <input type="text" class="ai-input" id="ai-setting-shortcut-reply" value="Ctrl+Space" readonly title="智能回复建议快捷键" aria-label="智能回复建议快捷键">
                    <button class="ai-btn ai-btn--small" onclick="this.previousElementSibling.readOnly = false">编辑</button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 通知设置 -->
            <div class="ai-settings-panel" data-settings-panel="notifications">
              <div class="ai-settings__group">
                <div class="ai-settings__group-title">通知行为</div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-desktop-notifications">桌面通知</label>
                    <div class="ai-settings__item-desc">显示系统桌面通知</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-desktop-notifications" checked title="桌面通知" aria-label="桌面通知">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-sound-notifications">声音提醒</label>
                    <div class="ai-settings__item-desc">播放通知声音</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-sound-notifications" title="声音提醒" aria-label="声音提醒">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-analysis-notifications">分析完成通知</label>
                    <div class="ai-settings__item-desc">内容分析完成时显示通知</div>
                  </div>
                  <div class="ai-settings__control">
                    <div class="ai-switch">
                      <input type="checkbox" id="ai-setting-analysis-notifications" checked title="分析完成通知" aria-label="分析完成通知">
                      <span class="ai-switch__slider"></span>
                    </div>
                  </div>
                </div>
                <div class="ai-settings__item">
                  <div class="ai-settings__item-info">
                    <label class="ai-settings__item-label" for="ai-setting-notification-duration">通知持续时间</label>
                    <div class="ai-settings__item-desc">通知显示的持续时间（秒）</div>
                  </div>
                  <div class="ai-settings__control">
                    <input type="number" class="ai-input ai-input--small" id="ai-setting-notification-duration" value="5" min="1" max="30" title="通知持续时间" aria-label="通知持续时间">
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
        
        <!-- 设置模态框底部 -->
        <div class="ai-modal__footer">
          <div class="ai-modal__footer-info">
            <span class="ai-text-secondary">设置将自动保存</span>
          </div>
          <div class="ai-modal__footer-actions">
            <button class="ai-btn ai-btn--secondary" id="ai-settings-reset-btn">重置设置</button>
            <button class="ai-btn ai-btn--secondary" id="ai-settings-export-btn-modal">导出设置</button>
            <button class="ai-btn ai-btn--secondary" id="ai-settings-import-btn-modal">导入设置</button>
            <button class="ai-btn ai-btn--primary" id="ai-settings-save-btn">保存设置</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 通知面板 -->
  <div id="ai-notifications-panel" class="ai-notifications-panel hidden">
    <div class="ai-notifications-panel__header">
      <h4>通知</h4>
      <button id="ai-notifications-clear-btn" class="ai-btn ai-btn--small">清空</button>
    </div>
    <div id="ai-notifications-list" class="ai-notifications-panel__list">
      <div class="ai-placeholder">
        <span class="ai-placeholder__icon">🔔</span>
        <p class="ai-placeholder__text">暂无通知</p>
      </div>
    </div>
  </div>
  
  <!-- 加载指示器 -->
  <div id="ai-loading-indicator" class="ai-loading hidden">
    <div class="ai-loading__backdrop"></div>
    <div class="ai-loading__content">
      <div class="ai-loading__spinner"></div>
      <div class="ai-loading__text">处理中...</div>
    </div>
  </div>
  
  <!-- 确认对话框 -->
  <div id="ai-confirm-dialog" class="ai-modal hidden">
    <div class="ai-modal__overlay"></div>
    <div class="ai-modal__content ai-modal__content--small">
      <div class="ai-modal__header">
        <h3 class="ai-modal__title" id="ai-confirm-title">确认操作</h3>
      </div>
      <div class="ai-modal__body">
        <p id="ai-confirm-message">确定要执行此操作吗？</p>
      </div>
      <div class="ai-modal__footer">
        <button id="ai-confirm-cancel-btn" class="ai-btn">取消</button>
        <button id="ai-confirm-ok-btn" class="ai-btn ai-btn--primary">确认</button>
      </div>
    </div>
  </div>
  
  <!-- #endregion -->
  
  <!-- JavaScript模块 -->
  <script type="module" src="aiSidebarPanel.js"></script>
</body>
</html> 