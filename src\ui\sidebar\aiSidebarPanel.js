/**
 * @file AI侧边栏面板主控制器 - 第四阶段UI集成版
 * @description 管理侧边栏UI交互、消息通信、功能集成等核心功能
 * @version 4.0.0
 * @since 2024-01-01
 */

// 引入统一的消息类型常量，避免魔法字符串混用
import { MESSAGE_TYPES } from '../../shared/messageTypes.js';

/**
 * @class AiSidebarPanel - AI侧边栏面板主控制类
 * @description 负责侧边栏所有UI交互和业务逻辑的统一管理，集成第三阶段所有功能
 */
class AiSidebarPanel {
  /**
   * @function constructor - 构造函数
   * @description 初始化侧边栏面板，设置事件监听器和基础状态
   */
  constructor() {
    // 初始化状态管理
    this.initializeState();
    
    // 初始化DOM元素引用
    this.initializeElements();
    
    // 设置事件监听器
    this.setupEventListeners();
    
    // 建立与后台的通信连接
    this.setupMessageConnection();
    
    // 初始化第三阶段功能模块
    this.initializeThirdPhaseModules();
    
    // 启动界面
    this.initializeInterface();
    
    console.log('🚀 AI侧边栏面板第四阶段已初始化完成');
  }

  /**
   * @function initializeState - 初始化状态管理
   * @description 设置面板的初始状态和数据结构
   */
  initializeState() {
    // 当前活动主标签页
    this.activeMainTab = 'chat';
    
    // 对话历史记录
    this.messageHistory = [];
    
    // 连接状态
    this.connectionStatus = 'ready';
    
    // 各功能模块状态
    this.moduleStates = {
      notion: { connected: false, syncing: false },
      settings: { loaded: false },
      analysis: { running: false },
      cursor: { enabled: false }
    };
    
    // 通知系统
    this.notifications = [];
    
    // 性能监控
    this.performanceMetrics = {
      startTime: Date.now(),
      loadTime: 0,
      messageCount: 0
    };
  }

  /**
   * @function initializeElements - 初始化DOM元素引用
   * @description 获取并缓存关键DOM元素的引用，提高性能
   */
  initializeElements() {
    // 主容器和布局
    this.container = document.getElementById('ai-sidebar-container');
    this.header = this.container?.querySelector('.ai-sidebar__header');
    this.mainTabs = this.container?.querySelector('.ai-sidebar__main-tabs');
    this.mainContent = this.container?.querySelector('.ai-sidebar__main');
    this.footer = this.container?.querySelector('.ai-sidebar__footer');
    
    // 顶部导航元素
    this.connectionStatus = document.getElementById('ai-connection-status');
    this.notificationsBtn = document.getElementById('ai-sidebar-notifications-btn');
    this.settingsBtn = document.getElementById('ai-sidebar-settings-btn');
    this.minimizeBtn = document.getElementById('ai-sidebar-minimize-btn');
    
    // 主标签页
    this.mainTabButtons = {
      chat: document.getElementById('ai-main-tab-chat'),
      settings: document.getElementById('ai-main-tab-settings'),
      notion: document.getElementById('ai-main-tab-notion'),
      analysis: document.getElementById('ai-main-tab-analysis'),
      enhance: document.getElementById('ai-main-tab-enhance')
    };
    
    // 面板容器
    this.panels = {
      chat: document.getElementById('ai-panel-chat'),
      settings: document.getElementById('ai-panel-settings'),
      notion: document.getElementById('ai-panel-notion'),
      analysis: document.getElementById('ai-panel-analysis'),
      enhance: document.getElementById('ai-panel-enhance')
    };
    
    // 对话面板元素
    this.chatElements = {
      messages: document.getElementById('ai-chat-messages'),
      input: document.getElementById('ai-chat-input'),
      sendBtn: document.getElementById('ai-chat-send-btn'),
      suggestions: document.getElementById('ai-chat-suggestions')
    };
    
    // 为后续方法提供快捷别名，避免多处DOM查询
    this.messagesContainer = this.chatElements.messages;
    this.chatInput = this.chatElements.input;
    this.sendBtn = this.chatElements.sendBtn;
    
    // 工具栏元素
    this.toolbarElements = this.initializeToolbarElements();
    
    // 状态指示器
    this.statusElements = {
      footer: document.getElementById('ai-status-text'),
      connection: this.connectionStatus
    };
    
    // 分析面板结果容器
    this.analysisResults = document.getElementById('ai-analysis-results');
    
    // 全局加载指示器
    this.loadingIndicator = document.getElementById('ai-loading-indicator');
  }
  
  /**
   * @function initializeToolbarElements - 初始化工具栏元素
   * @description 获取各面板的工具栏元素引用
   */
  initializeToolbarElements() {
    return {
      chat: {
        analyzeBtn: document.getElementById('ai-chat-analyze-btn'),
        clearBtn: document.getElementById('ai-chat-clear-btn'),
        exportBtn: document.getElementById('ai-chat-export-btn')
      },
      settings: {
        saveBtn: document.getElementById('ai-settings-save-btn'),
        resetBtn: document.getElementById('ai-settings-reset-btn'),
        exportBtn: document.getElementById('ai-settings-export-btn'),
        importBtn: document.getElementById('ai-settings-import-btn')
      },
      notion: {
        connectBtn: document.getElementById('ai-notion-connect-btn'),
        syncBtn: document.getElementById('ai-notion-sync-btn'),
        refreshBtn: document.getElementById('ai-notion-refresh-btn')
      },
      analysis: {
        startBtn: document.getElementById('ai-analysis-start-btn'),
        compareBtn: document.getElementById('ai-analysis-compare-btn'),
        trendBtn: document.getElementById('ai-trend-refresh-btn'),
        exportBtn: document.getElementById('ai-analysis-export-btn')
      },
      enhance: {
        enableBtn: document.getElementById('ai-cursor-enable-btn'),
        configBtn: document.getElementById('ai-cursor-config-btn'),
        testBtn: document.getElementById('ai-cursor-test-btn')
      }
    };
  }

  /**
   * @function setupEventListeners - 设置事件监听器
   * @description 为所有交互元素添加事件监听器
   */
  setupEventListeners() {
    // 顶部导航事件
    this.setupHeaderEvents();
    
    // 主标签页切换事件
    this.setupMainTabEvents();
    
    // 对话面板事件
    this.setupChatEvents();
    
    // 设置面板事件
    this.setupSettingsEvents();
    
    // Notion面板事件
    this.setupNotionEvents();
    
    // 分析面板事件
    this.setupAnalysisEvents();
    
    // 增强面板事件
    this.setupEnhanceEvents();
    
    // 全局事件
    this.setupGlobalEvents();
  }
  
  /**
   * @function setupHeaderEvents - 设置顶部导航事件
   * @description 设置顶部按钮的事件监听器
   */
  setupHeaderEvents() {
    // 通知按钮
    this.notificationsBtn?.addEventListener('click', () => this.toggleNotifications());
    
    // 设置按钮
    this.settingsBtn?.addEventListener('click', () => this.switchMainTab('settings'));
    
    // 最小化按钮
    this.minimizeBtn?.addEventListener('click', () => this.minimizeSidebar());
  }
  
  /**
   * @function setupMainTabEvents - 设置主标签页事件
   * @description 设置主标签页切换的事件监听器
   */
  setupMainTabEvents() {
    Object.entries(this.mainTabButtons).forEach(([tabName, button]) => {
      button?.addEventListener('click', () => this.switchMainTab(tabName));
    });
  }
  
  /**
   * @function setupChatEvents - 设置对话面板事件
   * @description 设置对话相关的事件监听器
   */
  setupChatEvents() {
    const { input, sendBtn } = this.chatElements;
    const { analyzeBtn, clearBtn, exportBtn } = this.toolbarElements.chat;
    
    // 发送消息
    sendBtn?.addEventListener('click', () => this.handleSendMessage());
    input?.addEventListener('keydown', (e) => this.handleInputKeydown(e));
    input?.addEventListener('input', () => this.handleInputChange());
    
    // 工具栏按钮
    analyzeBtn?.addEventListener('click', () => this.analyzeCurrentPage());
    clearBtn?.addEventListener('click', () => this.clearChatHistory());
    exportBtn?.addEventListener('click', () => this.exportChatHistory());
  }
  
  /**
   * @function setupSettingsEvents - 设置设置面板事件
   * @description 设置设置面板的事件监听器
   */
  setupSettingsEvents() {
    // 设置按钮点击事件
    const settingsBtn = this.container.querySelector('#ai-settings-btn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        this.showSettingsModal();
      });
    }

    // 设置模态框相关事件
    this.setupSettingsModalEvents();
  }
  
  /**
   * @function setupNotionEvents - 设置Notion面板事件
   * @description 设置Notion集成的事件监听器
   */
  setupNotionEvents() {
    const { connectBtn, syncBtn, refreshBtn } = this.toolbarElements.notion;

    // 工具栏按钮
    connectBtn?.addEventListener('click', () => this.connectNotion());
    syncBtn?.addEventListener('click', () => this.syncNotion());
    refreshBtn?.addEventListener('click', () => this.refreshNotionData());

    // 设置Notion Token输入处理
    this.setupNotionTokenInput();
  }
  
  /**
   * @function setupAnalysisEvents - 设置分析面板事件
   * @description 设置分析功能的事件监听器
   */
  setupAnalysisEvents() {
    const { startBtn, compareBtn, trendBtn, exportBtn } = this.toolbarElements.analysis;
    
    // 工具栏按钮
    startBtn?.addEventListener('click', () => this.startPageAnalysis());
    compareBtn?.addEventListener('click', () => this.startPageComparison());
    trendBtn?.addEventListener('click', () => this.refreshTrendAnalysis());
    exportBtn?.addEventListener('click', () => this.exportAnalysisResults());
    
    // 分析控件事件
    this.setupAnalysisControlEvents();
  }
  
  /**
   * @function setupEnhanceEvents - 设置增强面板事件
   * @description 设置光标增强功能的事件监听器
   */
  setupEnhanceEvents() {
    const { enableBtn, configBtn, testBtn } = this.toolbarElements.enhance;
    
    // 工具栏按钮
    enableBtn?.addEventListener('click', () => this.toggleCursorEnhancement());
    configBtn?.addEventListener('click', () => this.configureCursorEnhancement());
    testBtn?.addEventListener('click', () => this.testCursorEnhancement());
  }
  
  /**
   * @function setupGlobalEvents - 设置全局事件
   * @description 设置全局的事件监听器
   */
  setupGlobalEvents() {
    // 窗口大小变化
    window.addEventListener('resize', () => this.handleResize());
    
    // 键盘快捷键
    document.addEventListener('keydown', (e) => this.handleGlobalKeydown(e));
    
    // 页面可见性变化
    document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
  }

  /**
   * @function initializeThirdPhaseModules - 初始化第三阶段功能模块
   * @description 初始化Notion、设置、分析、增强等功能模块
   */
  async initializeThirdPhaseModules() {
    try {
      console.log('🔧 开始初始化第三阶段功能模块...');

      // 等待Service Worker完全初始化
      await this.waitForServiceWorkerReady();

      // 初始化设置管理器
      await this.initializeSettingsManager();

      // 初始化Notion连接器
      await this.initializeNotionConnector();

      // 初始化高级分析器
      await this.initializeAdvancedAnalyzer();

      // 初始化光标增强器
      await this.initializeCursorEnhancer();

      console.log('✅ 第三阶段功能模块初始化完成');

      // 第四阶段新增：初始化分析面板功能
      console.log('🔧 开始初始化第四阶段UI集成功能...');
      this.setupAnalysisControlEvents();

      // 第四阶段新增：初始化增强面板功能
      this.setupEnhanceTabEvents();
      this.setupTemplateEvents();

      console.log('✅ 第四阶段UI集成功能初始化完成');

    } catch (error) {
      console.error('❌ 功能模块初始化失败:', error);
      this.showNotification('功能模块初始化失败', 'error');
    }
  }

  /**
   * @function waitForServiceWorkerReady - 等待Service Worker就绪
   * @description 确保Service Worker完全初始化后再进行模块初始化
   * @returns {Promise<void>}
   */
  async waitForServiceWorkerReady() {
    const maxRetries = 10;
    const retryDelay = 500; // 500ms

    for (let i = 0; i < maxRetries; i++) {
      try {
        console.log(`[初始化] 检查Service Worker状态 (${i + 1}/${maxRetries})`);

        const response = await this.sendToBackground({
          type: 'ai:status:get',
          data: { component: 'service-worker' }
        });

        if (response && response.success && response.data?.initialized) {
          console.log('✅ Service Worker已就绪');
          return;
        }

        console.log('⏳ Service Worker尚未就绪，等待中...');
        await new Promise(resolve => setTimeout(resolve, retryDelay));

      } catch (error) {
        console.warn(`[初始化] Service Worker检查失败 (${i + 1}/${maxRetries}):`, error.message);

        if (i === maxRetries - 1) {
          console.warn('⚠️ Service Worker检查超时，继续初始化（可能影响功能）');
          return;
        }

        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }
  
  /**
   * @function initializeInterface - 初始化界面
   * @description 设置界面的初始状态和显示
   */
  initializeInterface() {
    // 设置默认活动标签页
    this.switchMainTab('chat');
    
    // 更新连接状态
    this.updateConnectionStatus('ready');
    
    // 更新状态栏
    this.updateStatusBar('AI侧边栏助手已就绪');
    
    // 记录加载完成时间
    this.performanceMetrics.loadTime = Date.now() - this.performanceMetrics.startTime;
    
    console.log(`🎉 界面初始化完成，加载耗时: ${this.performanceMetrics.loadTime}ms`);
  }

  /**
   * @function setupMessageConnection - 建立消息通信连接
   * @description 设置与background和content scripts的消息通信
   */
  setupMessageConnection() {
    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleBackgroundMessage(message, sendResponse);
    });
    
    // 发送连接确认消息
    this.sendToBackground({
      type: 'SIDEBAR_CONNECTED',
      timestamp: Date.now()
    });
  }

  /**
   * @function switchMainTab - 切换主标签页
   * @param {string} tabName - 标签页名称 ('chat' | 'settings' | 'notion' | 'analysis' | 'enhance')
   * @description 切换侧边栏主标签页显示
   */
  switchMainTab(tabName) {
    // 移除所有标签页的活动状态
    Object.values(this.mainTabButtons).forEach(button => {
      button?.classList.remove('ai-sidebar__main-tab--active');
    });
    
    Object.values(this.panels).forEach(panel => {
      panel?.classList.remove('ai-sidebar__panel--active');
    });
    
    // 激活指定标签页
    const targetButton = this.mainTabButtons[tabName];
    const targetPanel = this.panels[tabName];
    
    if (targetButton && targetPanel) {
      targetButton.classList.add('ai-sidebar__main-tab--active');
      targetPanel.classList.add('ai-sidebar__panel--active');
      
      this.activeMainTab = tabName;
      
      // 触发标签页激活事件
      this.onTabActivated(tabName);
      
      console.log(`🔄 切换到主标签页: ${tabName}`);
    } else {
      console.warn(`⚠️ 无法找到标签页: ${tabName}`);
    }
  }
  
  /**
   * @function onTabActivated - 标签页激活回调
   * @param {string} tabName - 激活的标签页名称
   * @description 处理标签页激活时的特定逻辑
   */
  onTabActivated(tabName) {
    try {
      console.log(`[标签页] 激活标签页: ${tabName}`);

      switch (tabName) {
        case 'chat':
          if (typeof this.onChatTabActivated === 'function') {
            this.onChatTabActivated();
          } else {
            console.error('[标签页] onChatTabActivated 方法不存在');
          }
          break;
        case 'settings':
          if (typeof this.onSettingsTabActivated === 'function') {
            this.onSettingsTabActivated();
          } else {
            console.error('[标签页] onSettingsTabActivated 方法不存在');
          }
          break;
        case 'notion':
          if (typeof this.onNotionTabActivated === 'function') {
            this.onNotionTabActivated();
          } else {
            console.error('[标签页] onNotionTabActivated 方法不存在');
          }
          break;
        case 'analysis':
          if (typeof this.onAnalysisTabActivated === 'function') {
            this.onAnalysisTabActivated();
          } else {
            console.error('[标签页] onAnalysisTabActivated 方法不存在');
          }
          break;
        case 'enhance':
          if (typeof this.onEnhanceTabActivated === 'function') {
            this.onEnhanceTabActivated();
          } else {
            console.error('[标签页] onEnhanceTabActivated 方法不存在');
          }
          break;
        default:
          console.warn(`[标签页] 未知的标签页类型: ${tabName}`);
      }
    } catch (error) {
      console.error(`[标签页] 激活标签页失败 (${tabName}):`, error);
    }
  }
  
  /**
   * @function updateConnectionStatus - 更新连接状态
   * @param {string} status - 连接状态 ('ready' | 'connecting' | 'connected' | 'disconnected' | 'error')
   * @description 更新顶部连接状态指示器
   */
  updateConnectionStatus(status) {
    const dotEl = this.statusElements?.connection;
    if (!dotEl) return;

    // 移除旧的状态类
    dotEl.classList.remove(
      'ai-sidebar__status-dot--connected',
      'ai-sidebar__status-dot--connecting',
      'ai-sidebar__status-dot--disconnected'
    );

    // 根据状态添加对应的样式
    switch (status) {
      case 'ready':
      case 'connected':
        dotEl.classList.add('ai-sidebar__status-dot--connected');
        break;
      case 'connecting':
        dotEl.classList.add('ai-sidebar__status-dot--connecting');
        break;
      case 'disconnected':
      case 'error':
        dotEl.classList.add('ai-sidebar__status-dot--disconnected');
        break;
    }

    // 记录当前连接状态
    this.connectionStatusState = status;
  }
  
  /**
   * @function updateStatusBar - 更新状态栏
   * @param {string} message - 状态消息
   * @description 更新底部状态栏显示的消息
   */
  updateStatusBar(message) {
    if (this.statusElements?.footer) {
      this.statusElements.footer.textContent = message;
    }
  }
  
  /**
   * @function showNotification - 显示通知
   * @param {string} message - 通知消息
   * @param {string} type - 通知类型 ('info' | 'success' | 'warning' | 'error')
   * @param {number} duration - 显示时长(毫秒)，默认3000
   * @description 显示顶部通知消息
   */
  showNotification(message, type = 'info', duration = 3000) {
    const notification = {
      id: Date.now(),
      message,
      type,
      timestamp: new Date()
    };
    
    this.notifications.unshift(notification);
    
    // 更新通知按钮徽章
    this.updateNotificationBadge();
    
    // 如果是错误类型，持续显示
    if (type !== 'error' && duration > 0) {
      setTimeout(() => {
        this.removeNotification(notification.id);
      }, duration);
    }
    
    console.log(`📢 通知 [${type.toUpperCase()}]: ${message}`);
  }
  
  /**
   * @function updateNotificationBadge - 更新通知徽章
   * @description 更新通知按钮上的未读徽章数量
   */
  updateNotificationBadge() {
    const badge = this.notificationsBtn?.querySelector('.ai-badge');
    const unreadCount = this.notifications.length;
    
    if (badge) {
      if (unreadCount > 0) {
        badge.textContent = unreadCount > 99 ? '99+' : unreadCount.toString();
        badge.style.display = 'flex';
      } else {
        badge.style.display = 'none';
      }
    }
  }

  /**
   * @function handleSendMessage - 处理发送消息
   * @description 发送用户输入的消息到AI服务
   */
  async handleSendMessage() {
    const messageText = this.chatInput?.value.trim();
    if (!messageText) return;
    
    // 显示用户消息
    this.addMessage(messageText, 'user');
    
    // 清空输入框
    this.chatInput.value = '';
    this.updateSendButton();
    
    // 显示加载状态
    this.showLoading();
    
    try {
      // 发送消息到background处理
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.CHAT_STREAM,
        payload: {
          text: messageText,
          history: this.messageHistory.slice(-10) // 只发送最近10条消息作为上下文
        }
      });
      
      if (response.success) {
        // 显示AI回复
        this.addMessage(response.reply, 'assistant');
      } else {
        this.addMessage('抱歉，发生了错误，请稍后重试。', 'assistant');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      this.addMessage('网络连接出现问题，请检查连接后重试。', 'assistant');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * @function addMessage - 添加消息到对话列表
   * @param {string} text - 消息文本
   * @param {string} type - 消息类型 ('user' | 'assistant')
   * @description 在对话界面中添加新消息
   */
  addMessage(text, type) {
    const messageElement = document.createElement('div');
    messageElement.className = `ai-chat__message ai-chat__message--${type}`;
    
    messageElement.innerHTML = `
      <div class="ai-chat__message-content">
        <div class="ai-chat__message-text">${this.escapeHtml(text)}</div>
      </div>
    `;
    
    this.messagesContainer?.appendChild(messageElement);
    
    // 记录到历史
    this.messageHistory.push({
      text,
      type,
      timestamp: Date.now()
    });
    
    // 滚动到底部
    this.scrollToBottom();
  }

  /**
   * @function handleInputKeydown - 处理输入框按键事件
   * @param {KeyboardEvent} event - 键盘事件
   * @description 处理Enter发送和其他快捷键
   */
  handleInputKeydown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.handleSendMessage();
    }
  }

  /**
   * @function handleInputChange - 处理输入框内容变化
   * @description 更新发送按钮状态
   */
  handleInputChange() {
    this.updateSendButton();
    const text = this.chatInput.value.trim();
    if (text.length > 2) {
      // 实际应调用API获取建议
      this.chatElements.suggestions.classList.remove('hidden');
    } else {
      this.chatElements.suggestions.classList.add('hidden');
    }
  }

  /**
   * @function updateSendButton - 更新发送按钮状态
   * @description 根据输入框内容启用/禁用发送按钮
   */
  updateSendButton() {
    const hasText = this.chatInput?.value.trim().length > 0;
    if (this.sendBtn) {
      this.sendBtn.disabled = !hasText;
    }
  }

  /**
   * @function handleAnalyzePage - 处理页面分析请求
   * @description 触发当前页面的智能分析
   */
  async handleAnalyzePage() {
    // 切换到分析面板，使用已有的 switchMainTab 方法
    this.switchMainTab('analysis');
    this.handleStartAnalysis();
  }

  /**
   * @function handleStartAnalysis - 开始页面分析
   * @description 启动页面内容分析流程
   */
  async handleStartAnalysis() {
    this.analysisStatus = 'analyzing';
    this.showLoading();
    
    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.ANALYSIS_REQUEST,
        payload: {
          pageInfo: await this.getCurrentPageInfo(),
          options: this.getAnalysisOptions()
        }
      });
      
      if (response.success) {
        this.displayAnalysisResults(response.analysis);
      } else {
        this.displayAnalysisError('分析失败，请稍后重试');
      }
    } catch (error) {
      console.error('页面分析失败:', error);
      this.showNotification('页面分析失败', 'error');
      this.updateStatusIndicator('分析失败');
    } finally {
      this.hideLoading();
      // 分析流程结束，重置状态
      this.analysisStatus = 'idle';
    }
  }

  /**
   * @function displayAnalysisResults - 显示分析结果
   * @param {Object} analysis - 分析结果数据
   * @description 在分析面板中显示页面分析结果
   */
  displayAnalysisResults(analysis) {
    if (!this.analysisResults) return;
    
    this.analysisResults.innerHTML = `
      <div class="ai-analysis__result">
        <h4>页面分析结果</h4>
        <div class="ai-analysis__summary">
          <p><strong>页面标题:</strong> ${this.escapeHtml(analysis.title || '未知')}</p>
          <p><strong>内容类型:</strong> ${this.escapeHtml(analysis.contentType || '网页')}</p>
          <p><strong>字数统计:</strong> ${analysis.wordCount || 0} 字</p>
        </div>
        ${analysis.summary ? `
          <div class="ai-analysis__content">
            <h5>内容摘要:</h5>
            <p>${this.escapeHtml(analysis.summary)}</p>
          </div>
        ` : ''}
        ${analysis.keyPoints && analysis.keyPoints.length > 0 ? `
          <div class="ai-analysis__points">
            <h5>关键信息:</h5>
            <ul>
              ${analysis.keyPoints.map(point => `<li>${this.escapeHtml(point)}</li>`).join('')}
            </ul>
          </div>
        ` : ''}
      </div>
    `;
  }

  /**
   * @function handleSmartReply - 处理智能回复功能
   * @description 基于页面内容生成智能回复建议
   */
  async handleSmartReply() {
    console.log('智能回复功能待实现');
    try {
      this.sendToBackground({
        type: MESSAGE_TYPES.SMART_REPLY_GENERATE,
        payload: {
          pageContent: '...' // 省略，应从内容脚本获取
        }
      });
    } catch (error) {
      console.error('智能回复生成失败:', error);
      this.addMessage('智能回复功能暂时不可用。', 'assistant');
    }
  }

  /**
   * @function showSettingsModal - 显示设置模态框
   * @description 打开设置配置界面
   */
  showSettingsModal() {
    console.log('显示设置模态框');
    if (this.settingsModal) {
      this.settingsModal.classList.remove('hidden');
      this.loadCurrentSettings(); // 打开时加载最新设置
    }
  }

  /**
   * @function setupSettingsModalEvents - 设置模态框事件
   * @description 为模态框添加关闭和交互事件
   */
  setupSettingsModalEvents() {
    const modal = document.getElementById('ai-settings-modal');
    const closeBtn = document.getElementById('ai-settings-modal-close');
    const overlay = modal?.querySelector('.ai-modal__overlay');
    
    // 关闭按钮事件
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hideSettingsModal();
      });
    }
    
    // 点击遮罩关闭
    if (overlay) {
      overlay.addEventListener('click', () => {
        this.hideSettingsModal();
      });
    }
    
    // ESC键关闭
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && modal && modal.style.display !== 'none') {
        this.hideSettingsModal();
      }
    });
    
    // 设置导航切换
    this.setupSettingsNavigation();
    
    // 设置表单控件事件
    this.setupSettingsFormEvents();
    
    // 设置操作按钮事件
    this.setupSettingsActionEvents();
  }

  /**
   * @function setupSettingsNavigation - 设置导航切换
   * @description 设置设置面板的导航切换逻辑
   */
  setupSettingsNavigation() {
    const navItems = document.querySelectorAll('.ai-settings-nav__item');
    const panels = document.querySelectorAll('.ai-settings-panel');
    
    navItems.forEach(item => {
      item.addEventListener('click', () => {
        const tabId = item.dataset.settingsTab;
        
        // 更新导航状态
        navItems.forEach(nav => nav.classList.remove('ai-settings-nav__item--active'));
        item.classList.add('ai-settings-nav__item--active');
        
        // 切换面板
        panels.forEach(panel => {
          panel.classList.remove('ai-settings-panel--active');
          if (panel.dataset.settingsPanel === tabId) {
            panel.classList.add('ai-settings-panel--active');
          }
        });
        
        // 记录当前选中的设置面板
        this.moduleStates.settings.activePanel = tabId;
      });
    });
  }

  /**
   * @function setupSettingsFormEvents - 设置表单控件事件
   * @description 设置设置面板的表单控件事件
   */
  setupSettingsFormEvents() {
    // 开关控件事件
    this.setupSwitchEvents();
    
    // 范围滑块事件
    this.setupRangeEvents();
    
    // 输入框事件
    this.setupInputEvents();
    
    // 选择框事件
    this.setupSelectEvents();
  }

  /**
   * @function setupSwitchEvents - 设置开关控件事件
   * @description 设置设置面板的开关控件事件
   */
  setupSwitchEvents() {
    const switches = document.querySelectorAll('.ai-switch input[type="checkbox"]');
    
    switches.forEach(switchEl => {
      switchEl.addEventListener('change', (e) => {
        const setting = e.target.id.replace('ai-setting-', '');
        const value = e.target.checked;
        
        // 保存设置到本地存储
        this.saveSetting(setting, value);
        
        // 特殊处理某些设置
        this.handleSpecialSettings(setting, value);
        
        console.log(`[设置] ${setting}: ${value}`);
      });
    });
  }

  /**
   * @function setupRangeEvents - 设置范围滑块事件
   * @description 设置设置面板的范围滑块事件
   */
  setupRangeEvents() {
    const ranges = document.querySelectorAll('.ai-range');
    
    ranges.forEach(range => {
      // 实时更新显示值
      range.addEventListener('input', (e) => {
        const valueDisplay = e.target.nextElementSibling;
        if (valueDisplay && valueDisplay.classList.contains('ai-settings__range-value')) {
          let displayValue = e.target.value;
          
          // 根据设置类型格式化显示值
          if (e.target.id.includes('memory') || e.target.id.includes('cache')) {
            displayValue += 'MB';
          } else if (e.target.id.includes('width')) {
            displayValue += 'px';
          }
          
          valueDisplay.textContent = displayValue;
        }
      });
      
      // 保存设置值
      range.addEventListener('change', (e) => {
        const setting = e.target.id.replace('ai-setting-', '');
        const value = parseInt(e.target.value);
        
        this.saveSetting(setting, value);
        console.log(`[设置] ${setting}: ${value}`);
      });
    });
  }

  /**
   * @function setupInputEvents - 设置输入框事件
   * @description 设置设置面板的输入框事件
   */
  setupInputEvents() {
    const inputs = document.querySelectorAll('.ai-input');
    
    inputs.forEach(input => {
      // 密码框显示/隐藏切换
      if (input.type === 'password') {
        this.addPasswordToggle(input);
      }
      
      // 保存输入值
      input.addEventListener('blur', (e) => {
        const setting = e.target.id.replace('ai-setting-', '');
        let value = e.target.value;
        
        // 数字类型转换
        if (e.target.type === 'number') {
          value = parseInt(value) || 0;
        }
        
        this.saveSetting(setting, value);
        console.log(`[设置] ${setting}: ${value}`);
      });
      
      // 实时验证
      input.addEventListener('input', (e) => {
        this.validateInput(e.target);
      });
    });
  }

  /**
   * @function setupSelectEvents - 设置选择框事件
   * @description 设置设置面板的选择框事件
   */
  setupSelectEvents() {
    const selects = document.querySelectorAll('.ai-select');
    
    selects.forEach(select => {
      select.addEventListener('change', (e) => {
        const setting = e.target.id.replace('ai-setting-', '');
        const value = e.target.value;
        
        this.saveSetting(setting, value);
        
        // 特殊处理某些设置
        this.handleSpecialSettings(setting, value);
        
        console.log(`[设置] ${setting}: ${value}`);
      });
    });
  }

  /**
   * @function setupSettingsActionEvents - 设置操作按钮事件
   * @description 设置设置面板的操作按钮事件
   */
  setupSettingsActionEvents() {
    // 主面板上的按钮
    this.toolbarElements.settings.refreshBtn?.addEventListener('click', () => this.loadCurrentSettings());
    this.toolbarElements.settings.exportBtn?.addEventListener('click', () => this.exportSettings());
    this.toolbarElements.settings.importBtn?.addEventListener('click', () => this.importSettings());
    
    // 模态框底部的按钮
    document.getElementById('ai-settings-reset-btn')?.addEventListener('click', () => this.resetAllSettings());
    document.getElementById('ai-settings-export-btn-modal')?.addEventListener('click', () => this.exportSettings());
    document.getElementById('ai-settings-import-btn-modal')?.addEventListener('click', () => this.importSettings());
    document.getElementById('ai-settings-save-btn')?.addEventListener('click', () => this.saveAllSettings());

    // 主面板上的保存/重置
    document.getElementById('ai-settings-reset-all')?.addEventListener('click', () => this.resetAllSettings());
    document.getElementById('ai-settings-save-all')?.addEventListener('click', () => this.saveAllSettings());
  }

  /**
   * @function hideSettingsModal - 隐藏设置模态框
   * @description 隐藏设置配置界面
   */
  hideSettingsModal() {
    if (this.settingsModal) {
      this.settingsModal.classList.add('hidden');
    }
  }

  /**
   * @function loadCurrentSettings - 加载当前设置值到表单
   * @description 从设置管理器加载当前设置值到表单
   */
  async loadCurrentSettings() {
    try {
      // 从设置管理器获取所有设置
      if (this.settingsManager) {
        const allSettings = await this.settingsManager.getAllSettings();
        
        // 填充表单控件
        Object.entries(allSettings).forEach(([key, value]) => {
          const element = document.getElementById(`ai-setting-${key}`);
          if (element) {
            if (element.type === 'checkbox') {
              element.checked = value;
            } else if (element.type === 'range') {
              element.value = value;
              // 更新显示值
              const valueDisplay = element.nextElementSibling;
              if (valueDisplay) {
                let displayValue = value;
                if (key.includes('memory') || key.includes('cache')) {
                  displayValue += 'MB';
                } else if (key.includes('width')) {
                  displayValue += 'px';
                }
                valueDisplay.textContent = displayValue;
              }
            } else {
              element.value = value;
            }
          }
        });
      }
      
      // API密钥已硬编码，无需从UI加载
    } catch (error) {
      console.error('[设置] 加载设置失败:', error);
      this.showNotification('加载设置失败', 'error');
    }
  }

  /**
   * @function saveSetting - 保存单个设置
   * @param {string} key - 设置键
   * @param {*} value - 设置值
   * @description 将设置保存到设置管理器
   */
  async saveSetting(key, value) {
    this.sendToBackground({
      type: MESSAGE_TYPES.SETTINGS_SET,
      payload: { key, value },
    });
  }

  /**
   * @function saveAllSettings - 保存所有设置
   * @description 将所有设置保存到设置管理器
   */
  async saveAllSettings() {
    try {
      // 显示全局加载指示器
      this.showLoading();
      
      // 收集所有表单数据
      const formData = this.collectFormData();
      
      // 验证设置
      const validation = this.validateSettings(formData);
      if (!validation.valid) {
        this.hideLoading();
        this.showNotification(validation.message, 'error');
        return;
      }
      
      // 保存设置到设置管理器（API密钥已硬编码，无需处理）
      if (this.settingsManager) {
        await this.settingsManager.updateSettings(formData);
      }
      
      this.hideLoading();
      this.showNotification('设置保存成功', 'success');
      
      console.log('[设置] 所有设置已保存');
    } catch (error) {
      this.hideLoading();
      console.error('[设置] 保存所有设置失败:', error);
      this.showNotification('保存设置失败', 'error');
    }
  }

  /**
   * @function resetAllSettings - 重置所有设置
   * @description 将所有设置重置到默认值
   */
  async resetAllSettings() {
    try {
      // 显示全局加载指示器
      this.showLoading();
      
      if (this.settingsManager) {
        await this.settingsManager.resetSettings();
      }
      
      // 重新加载设置到表单
      await this.loadCurrentSettings();
      
      this.hideLoading();
      this.showNotification('设置已重置', 'success');
      
      console.log('[设置] 所有设置已重置');
    } catch (error) {
      this.hideLoading();
      console.error('[设置] 重置设置失败:', error);
      this.showNotification('重置设置失败', 'error');
    }
  }

  /**
   * @function exportSettings - 导出设置
   * @description 将设置导出为JSON文件
   */
  async exportSettings() {
    console.log('导出设置');
    this.showLoading();
    this.sendToBackground({ type: MESSAGE_TYPES.SETTINGS_EXPORT });
  }

  /**
   * @function importSettings - 导入设置
   * @description 从JSON文件导入设置
   */
  async importSettings() {
    try {
      // 创建文件选择器
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      
      input.onchange = async (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = async (e) => {
            try {
              const settingsData = e.target.result;
              
              if (this.settingsManager) {
                await this.settingsManager.importSettings(settingsData);
                await this.loadCurrentSettings();
                this.showNotification('设置导入成功', 'success');
                console.log('[设置] 设置已导入');
              }
            } catch (error) {
              console.error('[设置] 导入设置失败:', error);
              this.showNotification('导入设置失败：文件格式无效', 'error');
            }
          };
          reader.readAsText(file);
        }
      };
      
      input.click();
    } catch (error) {
      console.error('[设置] 导入设置失败:', error);
      this.showNotification('导入设置失败', 'error');
    }
  }

  /**
   * @function collectFormData - 收集表单数据
   * @returns {Object} 表单数据
   * @description 收集所有设置输入
   */
  collectFormData() {
    const formData = {};
    
    // 收集所有设置输入
    const inputs = document.querySelectorAll('[id^="ai-setting-"]');
    inputs.forEach(input => {
      const key = input.id.replace('ai-setting-', '');
      let value = input.value;
      
      if (input.type === 'checkbox') {
        value = input.checked;
      } else if (input.type === 'number' || input.type === 'range') {
        value = parseInt(value) || 0;
      }
      
      formData[key] = value;
    });
    
    return formData;
  }

  /**
   * @function validateSettings - 验证设置数据
   * @param {Object} settings - 设置数据
   * @returns {Object} 验证结果
   * @description 验证设置数据
   */
  validateSettings(settings) {
    // API密钥已硬编码，无需验证
    
    // 数值范围验证
    if (settings['memory-limit'] && (settings['memory-limit'] < 20 || settings['memory-limit'] > 100)) {
      return { valid: false, message: '内存限制必须在20-100MB之间' };
    }
    
    if (settings['api-timeout'] && (settings['api-timeout'] < 5 || settings['api-timeout'] > 120)) {
      return { valid: false, message: 'API超时时间必须在5-120秒之间' };
    }
    
    return { valid: true };
  }

  /**
   * @function handleSpecialSettings - 处理特殊设置
   * @param {string} setting - 设置名称
   * @param {*} value - 设置值
   * @description 处理特殊设置逻辑
   */
  handleSpecialSettings(setting, value) {
    switch (setting) {
      case 'theme':
        this.applyTheme(value);
        break;
      case 'language':
        this.applyLanguage(value);
        break;
      case 'notion-sync':
        if (value && this.notionConnector) {
          this.notionConnector.connect();
        }
        break;
      case 'auto-sync':
        if (this.notionConnector) {
          if (value) {
            this.notionConnector.startAutoSync();
          } else {
            this.notionConnector.stopAutoSync();
          }
        }
        break;
    }
  }

  /**
   * @function applyTheme - 应用主题
   * @param {string} theme - 主题名称
   * @description 应用主题逻辑
   */
  applyTheme(theme) {
    const root = document.documentElement;
    
    if (theme === 'dark') {
      root.classList.add('ai-theme-dark');
    } else if (theme === 'light') {
      root.classList.remove('ai-theme-dark');
    } else {
      // 跟随系统
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (prefersDark) {
        root.classList.add('ai-theme-dark');
      } else {
        root.classList.remove('ai-theme-dark');
      }
    }
  }

  /**
   * @function applyLanguage - 应用语言设置
   * @param {string} language - 语言代码
   * @description 应用语言设置逻辑
   */
  applyLanguage(language) {
    // 这里可以添加国际化逻辑
    console.log(`[设置] 切换语言到: ${language}`);
  }

  /**
   * @function addPasswordToggle - 添加密码显示切换
   * @param {HTMLInputElement} input - 密码输入框
   * @description 添加密码显示切换逻辑
   */
  addPasswordToggle(input) {
    const wrapper = document.createElement('div');
    wrapper.className = 'ai-password-wrapper';
    
    const toggleBtn = document.createElement('button');
    toggleBtn.type = 'button';
    toggleBtn.className = 'ai-password-toggle';
    toggleBtn.innerHTML = '👁️';
    toggleBtn.title = '显示/隐藏密码';
    
    input.parentNode.insertBefore(wrapper, input);
    wrapper.appendChild(input);
    wrapper.appendChild(toggleBtn);
    
    toggleBtn.addEventListener('click', () => {
      if (input.type === 'password') {
        input.type = 'text';
        toggleBtn.innerHTML = '🙈';
      } else {
        input.type = 'password';
        toggleBtn.innerHTML = '👁️';
      }
    });
  }

  /**
   * @function validateInput - 验证输入框
   * @param {HTMLInputElement} input - 输入框元素
   * @description 验证输入框逻辑
   */
  validateInput(input) {
    let isValid = true;
    let message = '';
    
    // 移除之前的错误样式
    input.classList.remove('ai-input--error');
    
    // API密钥已硬编码，无需验证
    
    // 应用验证结果
    if (!isValid) {
      input.classList.add('ai-input--error');
      input.title = message;
    } else {
      input.title = '';
    }
    
    return isValid;
  }

  /**
   * @function showLoading - 显示加载指示器
   * @description 显示操作进行中的加载状态
   */
  showLoading() {
    if (this.loadingIndicator) {
      this.loadingIndicator.classList.remove('hidden');
    }
  }

  /**
   * @function hideLoading - 隐藏加载指示器
   * @description 隐藏加载状态指示器
   */
  hideLoading() {
    if (this.loadingIndicator) {
      this.loadingIndicator.classList.add('hidden');
    }
  }

  /**
   * @function updateStatusIndicator - 更新状态指示器
   * @param {string} status - 状态文本
   * @description 更新底部状态栏显示内容
   */
  updateStatusIndicator(status) {
    if (this.statusElements?.footer) {
      this.statusElements.footer.textContent = status;
    }
  }

  /**
   * @function sendToBackground - 发送消息到background脚本
   * @param {Object} message - 要发送的消息对象
   * @returns {Promise<Object>} 返回响应Promise
   * @description 与background脚本进行异步通信
   */
  sendToBackground(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  }

  /**
   * @function handleBackgroundMessage - 处理来自background的消息
   * @param {Object} message - 接收到的消息
   * @param {Function} sendResponse - 响应函数
   * @description 处理background脚本发送的消息
   */
  handleBackgroundMessage(message, sendResponse) {
    console.log('Sidebar received message:', message);
    const { type, payload } = message;

    switch (type) {
      case MESSAGE_TYPES.CHAT_STREAM:
        this.handleStreamingResponse(payload);
        break;
      case MESSAGE_TYPES.ANALYSIS_RESULT:
        this.displayAnalysisResults(payload);
        this.hideLoading();
        break;
      case MESSAGE_TYPES.SETTINGS_UPDATE:
        this.loadCurrentSettings();
        this.showNotification('设置已在后台更新', 'info');
        break;
      case MESSAGE_TYPES.NOTION_STATUS:
        this.updateNotionStatus(payload.status, payload.data);
        this.hideLoading();
        break;
      case MESSAGE_TYPES.NOTIFICATION_SHOW:
        this.showNotification(payload.message, payload.type);
        break;
    }
  }

  /**
   * @function escapeHtml - HTML转义
   * @param {string} text - 要转义的文本
   * @returns {string} 转义后的安全HTML文本
   * @description 防止XSS攻击的HTML转义处理
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * @function scrollToBottom - 滚动到消息底部
   * @description 将消息列表滚动到最新消息
   */
  scrollToBottom() {
    if (this.messagesContainer) {
      this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }
  }

  /**
   * @function handleResize - 处理窗口大小变化
   * @description 响应式布局调整
   */
  handleResize() {
    // 可以在这里添加响应式布局调整逻辑
    console.log('窗口大小已改变');
  }

  // #region 分析面板功能方法

  /**
   * @function setupAnalysisControlEvents - 设置分析控制事件
   * @description 处理分析选项和深度设置
   */
  setupAnalysisControlEvents() {
    // 分析标签页切换
    const analysisTabs = document.querySelectorAll('.ai-analysis__tab');
    const analysisContents = document.querySelectorAll('.ai-analysis__content');
    
    analysisTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const targetTab = tab.dataset.analysisTab;
        
        // 更新标签页状态
        analysisTabs.forEach(t => t.classList.remove('ai-analysis__tab--active'));
        tab.classList.add('ai-analysis__tab--active');
        
        // 更新内容显示
        analysisContents.forEach(content => {
          if (content.dataset.analysisContent === targetTab) {
            content.classList.add('ai-analysis__content--active');
          } else {
            content.classList.remove('ai-analysis__content--active');
          }
        });
        
        // 处理特殊标签页
        this.onAnalysisTabActivated(targetTab);
      });
    });

    // 分析选项复选框
    const analysisOptions = document.querySelectorAll('.ai-analysis__options input[type="checkbox"]');
    analysisOptions.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.updateAnalysisOptions();
      });
    });
    
    // 分析深度选择
    const depthSelect = document.getElementById('ai-analysis-depth');
    if (depthSelect) {
      depthSelect.addEventListener('change', () => {
        this.updateAnalysisDepth(depthSelect.value);
      });
    }

    // 对比分析事件
    const compareBtn = document.getElementById('ai-analysis-compare-btn');
    if (compareBtn) {
      compareBtn.addEventListener('click', () => {
        this.handleCompareAnalysis();
      });
    }

    // 趋势分析事件
    const trendRefreshBtn = document.getElementById('ai-trend-refresh-btn');
    if (trendRefreshBtn) {
      trendRefreshBtn.addEventListener('click', () => {
        this.refreshTrendAnalysis();
      });
    }

    // 历史记录清空
    const historyClearBtn = document.getElementById('ai-history-clear-btn');
    if (historyClearBtn) {
      historyClearBtn.addEventListener('click', () => {
        this.clearAnalysisHistory();
      });
    }
  }

  /**
   * @function startPageAnalysis - 开始页面分析
   * @description 启动当前页面的智能分析
   */
  async startPageAnalysis() {
    try {
      this.showLoading();
      this.updateStatusIndicator('正在分析页面...');

      // 获取分析选项
      const options = this.getAnalysisOptions();
      
      // 获取页面信息
      const pageInfo = await this.getCurrentPageInfo();
      
      // 显示页面信息卡片
      this.displayPageInfo(pageInfo);
      
      // 执行分析
      const analysisResult = await this.performPageAnalysis(pageInfo, options);
      
      // 显示分析结果
      this.displayDetailedAnalysisResults(analysisResult);
      
      // 保存到历史记录
      this.saveAnalysisToHistory(analysisResult);
      
      this.updateStatusIndicator('页面分析完成');
      this.showNotification('页面分析完成', 'success');
      
    } catch (error) {
      console.error('页面分析失败:', error);
      this.showNotification('页面分析失败', 'error');
      this.updateStatusIndicator('分析失败');
    } finally {
      this.hideLoading();
      // 分析流程结束，重置状态
      this.analysisStatus = 'idle';
    }
  }

  /**
   * @function getAnalysisOptions - 获取分析选项
   * @returns {Object} 分析选项配置
   */
  getAnalysisOptions() {
    return {
      text: document.getElementById('ai-analysis-text')?.checked || false,
      images: document.getElementById('ai-analysis-images')?.checked || false,
      links: document.getElementById('ai-analysis-links')?.checked || false,
      seo: document.getElementById('ai-analysis-seo')?.checked || false,
      depth: document.getElementById('ai-analysis-depth')?.value || 'standard'
    };
  }

  /**
   * @function getCurrentPageInfo - 获取当前页面信息
   * @returns {Promise<Object>} 页面信息对象
   */
  async getCurrentPageInfo() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // 注入内容脚本获取页面详细信息
      const pageData = await chrome.tabs.sendMessage(tab.id, {
        type: 'GET_PAGE_INFO'
      });

      return {
        title: tab.title || pageData?.title || '无标题',
        url: tab.url || '',
        wordCount: pageData?.wordCount || 0,
        readingTime: Math.ceil((pageData?.wordCount || 0) / 200),
        lastUpdated: new Date().toLocaleString(),
        content: pageData?.content || '',
        images: pageData?.images || [],
        links: pageData?.links || [],
        headings: pageData?.headings || {}
      };
    } catch (error) {
      console.error('获取页面信息失败:', error);
      return {
        title: '无法获取页面信息',
        url: window.location.href,
        wordCount: 0,
        readingTime: 0,
        lastUpdated: new Date().toLocaleString()
      };
    }
  }

  /**
   * @function displayPageInfo - 显示页面信息
   * @param {Object} pageInfo - 页面信息对象
   */
  displayPageInfo(pageInfo) {
    if (!pageInfo || !this.pageInfoContainer) return;

    this.pageInfoContainer.classList.remove('hidden');
    
    const titleEl = this.pageInfoContainer.querySelector('#ai-page-title');
    const urlEl = this.pageInfoContainer.querySelector('#ai-page-url');
    const wordCountEl = this.pageInfoContainer.querySelector('#ai-page-word-count');
    const readingTimeEl = this.pageInfoContainer.querySelector('#ai-page-reading-time');
    const lastUpdatedEl = this.pageInfoContainer.querySelector('#ai-page-last-updated');

    if (titleEl) titleEl.textContent = pageInfo.title;
    if (urlEl) urlEl.textContent = pageInfo.url;
    if (wordCountEl) wordCountEl.textContent = `${pageInfo.wordCount} 字`;
    if (readingTimeEl) readingTimeEl.textContent = `${pageInfo.readingTime} 分钟阅读`;
    if (lastUpdatedEl) lastUpdatedEl.textContent = pageInfo.lastUpdated;
  }

  /**
   * @function performPageAnalysis - 执行页面分析
   * @param {Object} pageInfo - 页面信息
   * @param {Object} options - 分析选项
   * @returns {Promise<Object>} 分析结果
   */
  async performPageAnalysis(pageInfo, options) {
    // 模拟分析过程（实际项目中会调用AI API）
    await new Promise(resolve => setTimeout(resolve, 2000));

    const result = {
      timestamp: Date.now(),
      pageInfo,
      options,
      content: {
        summary: this.generateContentSummary(pageInfo.content),
        keywords: this.extractKeywords(pageInfo.content),
        score: Math.floor(Math.random() * 20) + 80
      },
      structure: {
        headings: pageInfo.headings,
        paragraphs: this.countParagraphs(pageInfo.content),
        images: pageInfo.images.length,
        links: pageInfo.links.length,
        score: Math.floor(Math.random() * 30) + 70
      },
      seo: this.analyzeSEO(pageInfo),
      suggestions: this.generateSuggestions(pageInfo)
    };

    return result;
  }

  /**
   * @function displayDetailedAnalysisResults - 显示详细分析结果
   * @param {Object} result - 分析结果
   */
  displayDetailedAnalysisResults(result) {
    const resultsContainer = document.getElementById('ai-analysis-results');
    const template = document.getElementById('ai-analysis-template');
    
    if (!resultsContainer || !template) return;

    // 克隆模板
    const resultElement = template.cloneNode(true);
    resultElement.id = '';
    resultElement.style.display = 'block';

    // 填充内容摘要
    this.fillContentSummary(resultElement, result.content);
    
    // 填充结构分析
    this.fillStructureAnalysis(resultElement, result.structure);
    
    // 填充SEO分析
    this.fillSEOAnalysis(resultElement, result.seo);
    
    // 填充智能建议
    this.fillSuggestions(resultElement, result.suggestions);

    // 替换占位符内容
    resultsContainer.innerHTML = '';
    resultsContainer.appendChild(resultElement);
  }

  /**
   * @function fillContentSummary - 填充内容摘要
   * @param {HTMLElement} element - 结果元素
   * @param {Object} content - 内容分析结果
   */
  fillContentSummary(element, content) {
    const scoreElement = element.querySelector('#ai-score-content');
    const summaryElement = element.querySelector('#ai-content-summary');
    const keywordsElement = element.querySelector('#ai-content-keywords .ai-keywords__list');

    if (scoreElement) {
      scoreElement.textContent = content.score;
      scoreElement.setAttribute('data-score', content.score >= 85 ? 'high' : content.score >= 70 ? 'medium' : 'low');
    }

    if (summaryElement) {
      summaryElement.textContent = content.summary;
    }

    if (keywordsElement) {
      keywordsElement.innerHTML = content.keywords.map(keyword => 
        `<span class="ai-keyword-tag">${this.escapeHtml(keyword)}</span>`
      ).join('');
    }
  }

  /**
   * @function fillStructureAnalysis - 填充结构分析
   * @param {HTMLElement} element - 结果元素
   * @param {Object} structure - 结构分析结果
   */
  fillStructureAnalysis(element, structure) {
    const scoreElement = element.querySelector('#ai-score-structure');
    const headingsElement = element.querySelector('#ai-metric-headings');
    const paragraphsElement = element.querySelector('#ai-metric-paragraphs');
    const imagesElement = element.querySelector('#ai-metric-images');
    const linksElement = element.querySelector('#ai-metric-links');

    if (scoreElement) {
      scoreElement.textContent = structure.score;
      scoreElement.setAttribute('data-score', structure.score >= 85 ? 'high' : structure.score >= 70 ? 'medium' : 'low');
    }

    if (headingsElement) {
      const headingText = Object.entries(structure.headings)
        .map(([level, count]) => `${level}(${count})`)
        .join(' ');
      headingsElement.textContent = headingText || '无标题';
    }

    if (paragraphsElement) paragraphsElement.textContent = structure.paragraphs;
    if (imagesElement) imagesElement.textContent = structure.images;
    if (linksElement) linksElement.textContent = structure.links;
  }

  /**
   * @function onAnalysisTabActivated - 分析标签页激活回调
   * @param {string} tabName - 激活的标签页名称
   */
  onAnalysisTabActivated(tabName) {
    switch (tabName) {
      case 'current':
        // 当前页面分析
        break;
      case 'compare':
        this.loadComparablePages();
        break;
      case 'trend':
        this.loadTrendData();
        break;
      case 'history':
        this.loadAnalysisHistory();
        break;
    }
  }

  // #endregion

  // #region 增强面板功能方法

  /**
   * @function toggleCursorEnhancement - 切换光标增强
   * @description 启用或禁用光标增强功能
   */
  async toggleCursorEnhancement() {
    try {
      const isEnabled = await this.getCursorEnhancementStatus();
      const newStatus = !isEnabled;
      
      await this.setCursorEnhancementStatus(newStatus);
      this.updateCursorEnhancementUI(newStatus);
      
      const message = newStatus ? '光标增强已启用' : '光标增强已禁用';
      this.showNotification(message, 'success');
      
    } catch (error) {
      console.error('切换光标增强失败:', error);
      this.showNotification('操作失败', 'error');
    }
  }

  /**
   * @function updateCursorEnhancementUI - 更新光标增强UI状态
   * @param {boolean} isEnabled - 是否启用
   */
  updateCursorEnhancementUI(isEnabled) {
    const statusCard = document.getElementById('ai-cursor-status-card');
    const toggleBtn = document.getElementById('ai-enhance-toggle-btn');
    
    if (statusCard) {
      const icon = statusCard.querySelector('.ai-status-card__icon .ai-icon');
      const title = statusCard.querySelector('.ai-status-card__title');
      const text = statusCard.querySelector('.ai-status-card__text');
      
      if (isEnabled) {
        if (icon) icon.textContent = '✅';
        if (title) title.textContent = '光标增强已启用';
        if (text) text.textContent = '智能预测和自动补全功能正在运行';
      } else {
        if (icon) icon.textContent = '❌';
        if (title) title.textContent = '光标增强已禁用';
        if (text) text.textContent = '启用后可获得智能输入预测和自动补全功能';
      }
    }
    
    if (toggleBtn) {
      const btnText = toggleBtn.querySelector('span:last-child');
      if (btnText) {
        btnText.textContent = isEnabled ? '禁用增强' : '启用增强';
      }
    }
  }

  /**
   * @function setupEnhanceTabEvents - 设置增强标签页事件
   * @description 处理增强面板内的标签页切换
   */
  setupEnhanceTabEvents() {
    const enhanceTabs = document.querySelectorAll('.ai-enhance__tab');
    const enhanceContents = document.querySelectorAll('.ai-enhance__content');
    
    enhanceTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const targetTab = tab.dataset.enhanceTab;
        
        // 更新标签页状态
        enhanceTabs.forEach(t => t.classList.remove('ai-enhance__tab--active'));
        tab.classList.add('ai-enhance__tab--active');
        
        // 更新内容显示
        enhanceContents.forEach(content => {
          if (content.dataset.enhanceContent === targetTab) {
            content.classList.add('ai-enhance__content--active');
          } else {
            content.classList.remove('ai-enhance__content--active');
          }
        });
        
        // 处理特殊标签页
        this.onEnhanceTabActivated(targetTab);
      });
    });
  }

  /**
   * @function setupTemplateEvents - 设置模板事件
   * @description 处理模板管理功能
   */
  setupTemplateEvents() {
    // 模板分类筛选
    const categorySelect = document.getElementById('ai-templates-category');
    if (categorySelect) {
      categorySelect.addEventListener('change', () => {
        this.filterTemplates(categorySelect.value);
      });
    }

    // 模板搜索
    const searchInput = document.getElementById('ai-templates-search');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        this.searchTemplates(searchInput.value);
      });
    }

    // 添加模板按钮
    const addBtn = document.getElementById('ai-templates-add-btn');
    if (addBtn) {
      addBtn.addEventListener('click', () => {
        this.showAddTemplateDialog();
      });
    }

    // 模板卡片事件
    this.setupTemplateCardEvents();
  }

  /**
   * @function setupTemplateCardEvents - 设置模板卡片事件
   * @description 处理模板卡片的交互事件
   */
  setupTemplateCardEvents() {
    const templateCards = document.querySelectorAll('.ai-template-card');
    
    templateCards.forEach(card => {
      const useBtn = card.querySelector('.ai-template-card__btn[title="使用模板"]');
      const editBtn = card.querySelector('.ai-template-card__btn[title="编辑模板"]');
      
      if (useBtn) {
        useBtn.addEventListener('click', () => {
          const templateId = card.dataset.templateId;
          this.useTemplate(templateId);
        });
      }
      
      if (editBtn) {
        editBtn.addEventListener('click', () => {
          const templateId = card.dataset.templateId;
          this.editTemplate(templateId);
        });
      }
    });
  }

  /**
   * @function onEnhanceTabActivated - 增强标签页激活回调
   * @param {string} tabName - 激活的标签页名称
   */
  onEnhanceTabActivated(tabName) {
    switch (tabName) {
      case 'cursor':
        this.loadCursorEnhancementStatus();
        break;
      case 'shortcuts':
        // 快捷键设置已静态显示
        break;
      case 'templates':
        this.loadTemplates();
        break;
      case 'stats':
        this.loadEnhancementStats();
        break;
    }
  }

  /**
   * @function loadEnhancementStats - 加载增强功能统计
   * @description 显示使用统计数据
   */
  async loadEnhancementStats() {
    try {
      // 模拟加载统计数据
      const stats = {
        suggestions: Math.floor(Math.random() * 1000) + 500,
        accepted: Math.floor(Math.random() * 500) + 200,
        accuracy: Math.floor(Math.random() * 20) + 75,
        timeSaved: Math.floor(Math.random() * 3600) + 1800
      };

      // 更新统计显示
      this.updateStatsDisplay(stats);
      
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  }

  /**
   * @function updateStatsDisplay - 更新统计显示
   * @param {Object} stats - 统计数据
   */
  updateStatsDisplay(stats) {
    const suggestionsElement = document.getElementById('ai-stats-suggestions');
    const acceptedElement = document.getElementById('ai-stats-accepted');
    const accuracyElement = document.getElementById('ai-stats-accuracy');
    const timeSavedElement = document.getElementById('ai-stats-time-saved');

    if (suggestionsElement) suggestionsElement.textContent = stats.suggestions;
    if (acceptedElement) acceptedElement.textContent = stats.accepted;
    if (accuracyElement) accuracyElement.textContent = `${stats.accuracy}%`;
    if (timeSavedElement) {
      const hours = Math.floor(stats.timeSaved / 3600);
      const minutes = Math.floor((stats.timeSaved % 3600) / 60);
      timeSavedElement.textContent = `${hours}h ${minutes}m`;
    }
  }

  // #endregion

  // #region 工具方法

  /**
   * @function generateContentSummary - 生成内容摘要
   * @param {string} content - 页面内容
   * @returns {string} 内容摘要
   */
  generateContentSummary(content) {
    if (!content || content.length < 100) {
      return '页面内容较少，无法生成有效摘要。';
    }
    
    // 简单的摘要生成逻辑（实际项目中会使用AI）
    const sentences = content.split(/[.!?。！？]/);
    const summary = sentences.slice(0, 3).join('。') + '。';
    return summary.length > 200 ? summary.substring(0, 200) + '...' : summary;
  }

  /**
   * @function extractKeywords - 提取关键词
   * @param {string} content - 页面内容
   * @returns {Array<string>} 关键词数组
   */
  extractKeywords(content) {
    // 简单的关键词提取逻辑
    const words = content.toLowerCase().match(/[\u4e00-\u9fa5\w]+/g) || [];
    const wordFreq = {};
    
    words.forEach(word => {
      if (word.length > 1) {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      }
    });
    
    return Object.entries(wordFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 8)
      .map(([word]) => word);
  }

  /**
   * @function countParagraphs - 统计段落数量
   * @param {string} content - 页面内容
   * @returns {number} 段落数量
   */
  countParagraphs(content) {
    return (content.match(/\n\s*\n/g) || []).length + 1;
  }

  /**
   * @function analyzeSEO - 分析SEO
   * @param {Object} pageInfo - 页面信息
   * @returns {Object} SEO分析结果
   */
  analyzeSEO(pageInfo) {
    const items = [];
    let score = 0;
    
    // 标题检查
    if (pageInfo.title && pageInfo.title.length > 0) {
      items.push({ icon: '✅', text: '页面标题存在', status: 'pass' });
      score += 20;
    } else {
      items.push({ icon: '❌', text: '缺少页面标题', status: 'fail' });
    }
    
    // 内容长度检查
    if (pageInfo.wordCount > 300) {
      items.push({ icon: '✅', text: '内容长度充足', status: 'pass' });
      score += 20;
    } else {
      items.push({ icon: '⚠️', text: '内容长度不足', status: 'warning' });
      score += 10;
    }
    
    // 图片检查
    if (pageInfo.images && pageInfo.images.length > 0) {
      items.push({ icon: '✅', text: '包含图片内容', status: 'pass' });
      score += 15;
    } else {
      items.push({ icon: '⚠️', text: '缺少图片内容', status: 'warning' });
      score += 5;
    }
    
    // 链接检查
    if (pageInfo.links && pageInfo.links.length > 0) {
      items.push({ icon: '✅', text: '包含外部链接', status: 'pass' });
      score += 15;
    } else {
      items.push({ icon: '⚠️', text: '缺少外部链接', status: 'warning' });
      score += 5;
    }
    
    return { items, score: Math.min(score, 100) };
  }

  /**
   * @function generateSuggestions - 生成智能建议
   * @param {Object} pageInfo - 页面信息
   * @returns {Array<Object>} 建议列表
   */
  generateSuggestions(pageInfo) {
    const suggestions = [];
    
    if (pageInfo.wordCount < 300) {
      suggestions.push({
        icon: '📝',
        title: '增加内容长度',
        desc: '建议将页面内容扩展至300字以上，以提高搜索引擎友好度。',
        priority: 'high'
      });
    }
    
    if (!pageInfo.images || pageInfo.images.length === 0) {
      suggestions.push({
        icon: '🖼️',
        title: '添加相关图片',
        desc: '适当添加相关图片可以提升用户体验和页面吸引力。',
        priority: 'medium'
      });
    }
    
    suggestions.push({
      icon: '🔍',
      title: '优化关键词密度',
      desc: '合理使用关键词，避免过度堆砌，保持自然的内容流畅度。',
      priority: 'low'
    });
    
    return suggestions;
  }

  /**
   * @function fillSEOAnalysis - 填充SEO分析
   * @param {HTMLElement} element - 结果元素
   * @param {Object} seo - SEO分析结果
   */
  fillSEOAnalysis(element, seo) {
    const scoreElement = element.querySelector('#ai-score-seo');
    const itemsContainer = element.querySelector('#ai-seo-items');

    if (scoreElement) {
      scoreElement.textContent = seo.score;
      scoreElement.setAttribute('data-score', seo.score >= 85 ? 'high' : seo.score >= 70 ? 'medium' : 'low');
    }

    if (itemsContainer && seo.items) {
      itemsContainer.innerHTML = seo.items.map(item => 
        `<div class="ai-seo-item ai-seo-item--${item.status}">
          <span class="ai-seo-item__icon">${item.icon}</span>
          <span class="ai-seo-item__text">${this.escapeHtml(item.text)}</span>
        </div>`
      ).join('');
    }
  }

  /**
   * @function fillSuggestions - 填充智能建议
   * @param {HTMLElement} element - 结果元素
   * @param {Array} suggestions - 建议列表
   */
  fillSuggestions(element, suggestions) {
    const suggestionsContainer = element.querySelector('#ai-suggestions-list');

    if (suggestionsContainer && suggestions) {
      suggestionsContainer.innerHTML = suggestions.map(suggestion => 
        `<div class="ai-suggestion ai-suggestion--${suggestion.priority}">
          <div class="ai-suggestion__icon">${suggestion.icon}</div>
          <div class="ai-suggestion__content">
            <div class="ai-suggestion__title">${this.escapeHtml(suggestion.title)}</div>
            <div class="ai-suggestion__desc">${this.escapeHtml(suggestion.desc)}</div>
          </div>
        </div>`
      ).join('');
    }
  }

  /**
   * @function saveAnalysisToHistory - 保存分析结果到历史记录
   * @param {Object} result - 分析结果
   */
  saveAnalysisToHistory(result) {
    try {
      const history = JSON.parse(localStorage.getItem('ai-analysis-history') || '[]');
      history.unshift({
        id: `analysis_${result.timestamp}`,
        timestamp: result.timestamp,
        url: result.pageInfo.url,
        title: result.pageInfo.title,
        summary: result.content.summary.substring(0, 100) + '...',
        scores: {
          content: result.content.score,
          structure: result.structure.score,
          seo: result.seo.score
        }
      });
      
      // 只保留最近50条记录
      if (history.length > 50) {
        history.splice(50);
      }
      
      localStorage.setItem('ai-analysis-history', JSON.stringify(history));
    } catch (error) {
      console.error('保存分析历史失败:', error);
    }
  }

  /**
   * @function updateAnalysisOptions - 更新分析选项
   * @description 响应分析选项变化
   */
  updateAnalysisOptions() {
    const options = this.getAnalysisOptions();
    console.log('分析选项已更新:', options);
    
    // 可以在这里添加选项变化的响应逻辑
    if (Object.values(options).some(enabled => enabled)) {
      this.updateStatusIndicator('分析选项已配置');
    } else {
      this.updateStatusIndicator('请选择至少一个分析选项');
    }
  }

  /**
   * @function updateAnalysisDepth - 更新分析深度
   * @param {string} depth - 分析深度
   */
  updateAnalysisDepth(depth) {
    console.log('分析深度已更新:', depth);
    this.updateStatusIndicator(`分析深度: ${depth}`);
  }

  /**
   * @function handleCompareAnalysis - 处理对比分析
   * @description 启动页面对比分析功能
   */
  async handleCompareAnalysis() {
    this.showNotification('对比分析功能开发中...', 'info');
    console.log('启动对比分析');
  }

  /**
   * @function refreshTrendAnalysis - 刷新趋势分析
   * @description 刷新趋势分析数据
   */
  async refreshTrendAnalysis() {
    this.showNotification('趋势分析功能开发中...', 'info');
    console.log('刷新趋势分析');
  }

  /**
   * @function clearAnalysisHistory - 清空分析历史
   * @description 清空所有分析历史记录
   */
  clearAnalysisHistory() {
    if (confirm('确定要清空所有分析历史记录吗？此操作不可撤销。')) {
      localStorage.removeItem('ai-analysis-history');
      this.showNotification('分析历史已清空', 'success');
      console.log('分析历史已清空');
    }
  }

  /**
   * @function loadComparablePages - 加载可对比页面
   * @description 加载可用于对比分析的页面列表
   */
  loadComparablePages() {
    console.log('加载可对比页面列表');
    // 实现对比页面加载逻辑
  }

  /**
   * @function loadTrendData - 加载趋势数据
   * @description 加载趋势分析数据
   */
  loadTrendData() {
    console.log('加载趋势分析数据');
    // 实现趋势数据加载逻辑
  }

  /**
   * @function loadAnalysisHistory - 加载分析历史
   * @description 加载并显示分析历史记录
   */
  loadAnalysisHistory() {
    try {
      const history = JSON.parse(localStorage.getItem('ai-analysis-history') || '[]');
      console.log('加载分析历史:', history.length, '条记录');
      // 在这里可以更新UI显示历史记录
    } catch (error) {
      console.error('加载分析历史失败:', error);
    }
  }

  /**
   * @function getCursorEnhancementStatus - 获取光标增强状态
   * @returns {Promise<boolean>} 是否启用光标增强
   */
  async getCursorEnhancementStatus() {
    const status = await this.sendToBackground({
      type: MESSAGE_TYPES.CURSOR_ENHANCE_STATUS,
    });
    return status?.enabled;
  }

  /**
   * @function setCursorEnhancementStatus - 设置光标增强状态
   * @param {boolean} enabled - 是否启用
   */
  async setCursorEnhancementStatus(enabled) {
    this.sendToBackground({
      type: MESSAGE_TYPES.CURSOR_ENHANCE_TOGGLE,
      payload: { enabled },
    });
  }

  /**
   * @function filterTemplates - 筛选模板
   * @param {string} category - 模板分类
   */
  filterTemplates(category) {
    const templateCards = document.querySelectorAll('.ai-template-card');
    
    templateCards.forEach(card => {
      const cardCategory = card.dataset.category;
      if (category === 'all' || cardCategory === category) {
        card.style.display = 'block';
      } else {
        card.style.display = 'none';
      }
    });
    
    console.log('模板筛选:', category);
  }

  /**
   * @function searchTemplates - 搜索模板
   * @param {string} query - 搜索关键词
   */
  searchTemplates(query) {
    const templateCards = document.querySelectorAll('.ai-template-card');
    const searchTerm = query.toLowerCase();
    
    templateCards.forEach(card => {
      const title = card.querySelector('.ai-template-card__title')?.textContent.toLowerCase() || '';
      const preview = card.querySelector('.ai-template-card__preview')?.textContent.toLowerCase() || '';
      
      if (title.includes(searchTerm) || preview.includes(searchTerm)) {
        card.style.display = 'block';
      } else {
        card.style.display = 'none';
      }
    });
    
    console.log('模板搜索:', query);
  }

  /**
   * @function showAddTemplateDialog - 显示添加模板对话框
   * @description 显示创建新模板的对话框
   */
  showAddTemplateDialog() {
    this.showNotification('模板创建功能开发中...', 'info');
    console.log('显示添加模板对话框');
  }

  /**
   * @function useTemplate - 使用模板
   * @param {string} templateId - 模板ID
   */
  useTemplate(templateId) {
    this.showNotification(`正在使用模板: ${templateId}`, 'success');
    console.log('使用模板:', templateId);
  }

  /**
   * @function editTemplate - 编辑模板
   * @param {string} templateId - 模板ID
   */
  editTemplate(templateId) {
    this.showNotification(`正在编辑模板: ${templateId}`, 'info');
    console.log('编辑模板:', templateId);
  }

  /**
   * @function loadTemplates - 加载模板列表
   * @description 加载并显示模板库
   */
  loadTemplates() {
    console.log('加载模板列表');
    // 实现模板加载逻辑
  }

  // #endregion

  // #region 第三阶段模块初始化方法
  /**
   * @function initializeSettingsManager - 初始化设置管理器
   * @description 通过Background Service Worker初始化设置管理器
   * @returns {Promise<void>}
   */
  async initializeSettingsManager() {
    try {
      console.log('🔧 初始化设置管理器...');
      
      const response = await this.sendToBackground({
        type: 'ai:settings:get',
        data: { action: 'initialize' }
      });
      
      if (response && response.success) {
        this.moduleStates.settings = { enabled: true, initialized: true };
        console.log('✅ 设置管理器初始化完成');
      } else {
        throw new Error(response?.error || '设置管理器初始化失败');
      }
    } catch (error) {
      console.error('❌ 设置管理器初始化失败:', error);
      this.moduleStates.settings = { enabled: false, initialized: false };
      // 不抛出错误，允许其他模块继续初始化
    }
  }

  /**
   * @function initializeNotionConnector - 初始化Notion连接器
   * @description 通过Background Service Worker初始化Notion连接器
   * @returns {Promise<void>}
   */
  async initializeNotionConnector() {
    try {
      console.log('🔧 初始化Notion连接器...');
      
      const response = await this.sendToBackground({
        type: 'ai:notion:connect',
        data: { action: 'initialize' }
      });
      
      if (response && response.success) {
        this.moduleStates.notion = { enabled: true, initialized: true };
        console.log('✅ Notion连接器初始化完成');
      } else {
        console.warn('⚠️ Notion连接器初始化失败，但这是可选功能');
        this.moduleStates.notion = { enabled: false, initialized: false };
      }
    } catch (error) {
      console.error('❌ Notion连接器初始化失败:', error);
      this.moduleStates.notion = { enabled: false, initialized: false };
      // 不抛出错误，Notion是可选功能
    }
  }

  /**
   * @function onNotionTabActivated - Notion标签页激活回调
   * @description 处理Notion标签页激活时的逻辑
   */
  onNotionTabActivated() {
    console.log('🔄 Notion标签页已激活');

    // 更新Notion连接状态显示
    this.updateNotionStatus();

    // 如果已连接，刷新数据
    if (this.moduleStates.notion?.connected) {
      this.refreshNotionData();
    }
  }

  /**
   * @function connectNotion - 连接Notion
   * @description 启动Notion连接流程
   */
  async connectNotion() {
    console.log('发起Notion连接...');
    this.showLoading();
    this.sendToBackground({ type: MESSAGE_TYPES.NOTION_CONNECT });
  }

  /**
   * @function syncNotion - 同步到Notion
   * @description 将当前对话历史同步到Notion
   */
  async syncNotion() {
    console.log('手动触发Notion同步...');
    this.updateNotionStatus('syncing');
    this.sendToBackground({ type: MESSAGE_TYPES.NOTION_SYNC, payload: { force: true } });
  }

  /**
   * @function refreshNotionData - 刷新Notion数据
   * @description 刷新Notion相关数据显示和缓存
   */
  async refreshNotionData() {
    try {
      if (!this.moduleStates.notion?.connected) {
        return;
      }

      console.log('🔄 刷新Notion数据...');

      // 更新缓存
      const response = await this.sendToBackground({
        type: 'ai:notion:cache:update',
        data: { forceUpdate: true }
      });

      if (response && response.success) {
        console.log('✅ Notion缓存更新成功');
        this.showNotification(`缓存更新完成: ${response.totalPages} 个页面`, 'success');
        this.updateNotionStatus('connected', {
          cacheInfo: `缓存: ${response.totalPages} 页面`
        });
      } else {
        console.warn('⚠️ Notion缓存更新失败:', response?.error);
        this.updateNotionStatus('connected');
      }

    } catch (error) {
      console.error('❌ 刷新Notion数据失败:', error);
      this.showNotification('刷新失败: ' + error.message, 'error');
    }
  }

  /**
   * @function clearNotionCache - 清除Notion缓存
   * @description 清除本地Notion缓存
   */
  async clearNotionCache() {
    try {
      console.log('🗑️ 清除Notion缓存...');

      const response = await this.sendToBackground({
        type: 'ai:notion:cache:clear',
        data: {}
      });

      if (response && response.success) {
        console.log('✅ Notion缓存清除成功');
        this.showNotification('缓存已清除', 'success');
        this.updateNotionStatus('connected', { cacheInfo: '缓存已清除' });
      } else {
        console.error('❌ Notion缓存清除失败:', response?.error);
        this.showNotification('缓存清除失败: ' + (response?.error || '未知错误'), 'error');
      }

    } catch (error) {
      console.error('❌ 清除Notion缓存异常:', error);
      this.showNotification('缓存清除异常: ' + error.message, 'error');
    }
  }

  /**
   * @function getNotionCacheStatus - 获取Notion缓存状态
   * @description 获取当前Notion缓存的状态信息
   */
  async getNotionCacheStatus() {
    try {
      const response = await this.sendToBackground({
        type: 'ai:notion:cache:status',
        data: {}
      });

      if (response && response.success) {
        return response.status;
      } else {
        console.warn('⚠️ 获取缓存状态失败:', response?.error);
        return null;
      }

    } catch (error) {
      console.error('❌ 获取缓存状态异常:', error);
      return null;
    }
  }

  /**
   * @function updateNotionStatus - 更新Notion状态显示
   * @description 更新Notion连接状态的UI显示
   * @param {string} status - 状态 ('disconnected', 'connecting', 'connected', 'error')
   * @param {Object} data - 额外数据
   */
  updateNotionStatus(status = 'disconnected', data = {}) {
    const statusCard = document.getElementById('ai-notion-status-card');
    if (!statusCard) return;

    const iconElement = statusCard.querySelector('.ai-status-card__icon .ai-icon');
    const titleElement = statusCard.querySelector('.ai-status-card__title');
    const textElement = statusCard.querySelector('.ai-status-card__text');

    switch (status) {
      case 'connecting':
        iconElement.textContent = '🔄';
        titleElement.textContent = '连接中...';
        textElement.textContent = '正在连接到Notion，请稍候';
        break;
      case 'connected':
        iconElement.textContent = '✅';
        titleElement.textContent = '已连接';
        textElement.textContent = `已连接到Notion工作区${data.workspace ? ': ' + data.workspace : ''}`;
        break;
      case 'error':
        iconElement.textContent = '❌';
        titleElement.textContent = '连接失败';
        textElement.textContent = data.error || '连接Notion时发生错误';
        break;
      default: // disconnected
        iconElement.textContent = '❌';
        titleElement.textContent = '未连接';
        textElement.textContent = '点击"连接Notion"开始使用云端同步功能';
        break;
    }
  }

  /**
   * @function configureNotionToken - 配置Notion Integration Token
   * @description 通过UI配置Notion Integration Token
   * @param {string} token - Integration Token
   */
  async configureNotionToken(token) {
    try {
      if (!token || typeof token !== 'string' || token.trim() === '') {
        throw new Error('请提供有效的Integration Token');
      }

      console.log('🔧 配置Notion Integration Token...');

      // 保存Token到Chrome存储
      await chrome.storage.sync.set({ notion_integration_token: token.trim() });

      // 测试连接
      const response = await this.sendToBackground({
        type: 'ai:notion:connect',
        data: {}
      });

      if (response && response.success) {
        this.showNotification('Notion Integration Token配置成功', 'success');
        this.updateNotionStatus('connected', response);
        return true;
      } else {
        throw new Error(response?.error || 'Token验证失败');
      }

    } catch (error) {
      console.error('❌ 配置Notion Token失败:', error);
      this.showNotification('Token配置失败: ' + error.message, 'error');
      return false;
    }
  }

  /**
   * @function setupNotionTokenInput - 设置Notion Token输入处理
   * @description 为设置面板中的Notion Token输入框设置事件处理
   */
  setupNotionTokenInput() {
    const tokenInput = document.getElementById('ai-setting-notion-token');
    const toggleBtn = document.getElementById('ai-notion-token-toggle');

    if (tokenInput && toggleBtn) {
      // 切换密码显示/隐藏
      toggleBtn.addEventListener('click', () => {
        const isPassword = tokenInput.type === 'password';
        tokenInput.type = isPassword ? 'text' : 'password';
        toggleBtn.querySelector('.ai-icon').textContent = isPassword ? '🙈' : '👁️';
      });

      // Token输入变化时自动保存和验证
      let saveTimeout;
      tokenInput.addEventListener('input', () => {
        clearTimeout(saveTimeout);
        saveTimeout = setTimeout(async () => {
          const token = tokenInput.value.trim();
          if (token && token.startsWith('secret_') && token.length > 20) {
            await this.configureNotionToken(token);
          }
        }, 1000); // 1秒延迟自动保存
      });

      // 加载现有Token
      chrome.storage.sync.get(['notion_integration_token']).then(result => {
        if (result.notion_integration_token) {
          tokenInput.value = result.notion_integration_token;
        }
      });
    }
  }

  /**
   * @function initializeAdvancedAnalyzer - 初始化高级分析器
   * @description 通过Background Service Worker初始化高级分析器
   * @returns {Promise<void>}
   */
  async initializeAdvancedAnalyzer() {
    try {
      console.log('🔧 初始化高级分析器...');
      
      const response = await this.sendToBackground({
        type: 'ai:analysis:compare',
        data: { action: 'initialize' }
      });
      
      if (response && response.success) {
        this.moduleStates.analysis = { enabled: true, initialized: true };
        console.log('✅ 高级分析器初始化完成');
      } else {
        throw new Error(response?.error || '高级分析器初始化失败');
      }
    } catch (error) {
      console.error('❌ 高级分析器初始化失败:', error);
      this.moduleStates.analysis = { enabled: false, initialized: false };
      // 不抛出错误，允许其他模块继续初始化
    }
  }

  /**
   * @function initializeCursorEnhancer - 初始化光标增强器
   * @description 通过Background Service Worker初始化光标增强器
   * @returns {Promise<void>}
   */
  async initializeCursorEnhancer() {
    try {
      console.log('🔧 初始化光标增强器...');
      
      const response = await this.sendToBackground({
        type: 'ai:cursor:enhance',
        data: { action: 'initialize' }
      });
      
      if (response && response.success) {
        this.moduleStates.cursor = { enabled: true, initialized: true };
        console.log('✅ 光标增强器初始化完成');
        
        // 加载光标增强状态
        await this.loadCursorEnhancementStatus();
      } else {
        console.warn('⚠️ 光标增强器初始化失败，但这是可选功能');
        this.moduleStates.cursor = { enabled: false, initialized: false };
      }
    } catch (error) {
      console.error('❌ 光标增强器初始化失败:', error);
      this.moduleStates.cursor = { enabled: false, initialized: false };
      // 不抛出错误，光标增强是可选功能
    }
  }
  // #endregion

  // BEGIN_EDIT
  /**
   * @function handleGlobalKeydown - 全局快捷键处理
   * @param {KeyboardEvent} event - 键盘事件
   */
  handleGlobalKeydown(event) {
    // 示例：Esc 关闭模态框 / Ctrl+Shift+L 清空消息
    if (event.key === 'Escape') {
      this.hideSettingsModal?.();
    }
  }

  /**
   * @function handleVisibilityChange - 处理页面可见性变化
   */
  handleVisibilityChange() {
    if (document.visibilityState === 'visible') {
      this.updateStatusBar('页面已激活');
    } else {
      this.updateStatusBar('页面已隐藏');
    }
  }

  /**
   * @function toggleNotifications - 切换通知面板显示状态
   */
  toggleNotifications() {
    const panel = document.getElementById('ai-notifications-panel');
    if (panel) {
      panel.classList.toggle('hidden');
      if (!panel.classList.contains('hidden')) {
        // 清空角标
        this.notificationBadge.textContent = '0';
        this.notificationBadge.classList.add('hidden');
      }
    }
  }

  /**
   * @function exportAnalysisResults - 导出分析结果（占位）
   */
  exportAnalysisResults() {
    this.showNotification('导出功能暂未实现，敬请期待', 'info');
  }

  /**
   * @function onChatTabActivated - Chat 标签激活回调（占位）
   */
  onChatTabActivated() {
    // 目前仅确保输入框聚焦
    this.chatInput?.focus();
  }

  /**
   * @function onSettingsTabActivated - Settings 标签激活回调（占位）
   */
  onSettingsTabActivated() {
    // 加载当前设置（若尚未加载）
    if (!this.moduleStates.settings.loaded) {
      this.loadCurrentSettings?.();
      this.moduleStates.settings.loaded = true;
    }
  }
  // END_EDIT

  /**
   * @function displayAnalysisError - 显示分析错误信息
   * @param {string} message - 错误信息
   * @description 当分析流程出现错误时统一处理
   */
  displayAnalysisError(message) {
    // 使用统一通知栏显示错误，并更新状态指示器
    this.showNotification(message, 'error');
    this.updateStatusIndicator(message);
  }
}

// 等待DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  window.aiSidebarPanel = new AiSidebarPanel();
}); 