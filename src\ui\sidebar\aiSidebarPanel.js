/**
 * @file AI侧边栏面板主控制器 - 界面重构版
 * @description 管理简化后的对话中心界面、快捷模板系统、多风格回复等核心功能
 * @version 5.0.0
 * @since 2024-12-19
 */

// 引入统一的消息类型常量，支持多环境兼容
const MESSAGE_TYPES = window.MESSAGE_TYPES || globalThis.MESSAGE_TYPES || {};

/**
 * @class AiSidebarPanel - AI侧边栏面板主控制类
 * @description 负责重构后的简化界面交互和业务逻辑管理，专注于对话中心体验
 */
class AiSidebarPanel {
  /**
   * @function constructor - 构造函数
   * @description 初始化重构后的侧边栏面板，设置新的交互逻辑
   */
  constructor() {
    // 初始化状态管理
    this.initializeState();

    // 初始化DOM元素引用
    this.initializeElements();

    // 设置事件监听器
    this.setupEventListeners();

    // 建立与后台的通信连接
    this.setupMessageConnection();

    // 初始化快捷模板系统
    this.initializeTemplateSystem();

    // 初始化对话气泡交互
    this.initializeChatBubbleInteractions();

    // 启动界面
    this.initializeInterface();

    console.log('🚀 AI侧边栏面板界面重构版已初始化完成');
  }

  /**
   * @function initializeState - 初始化状态管理
   * @description 设置重构后面板的初始状态和数据结构
   */
  initializeState() {
    // 对话历史记录
    this.messageHistory = [];

    // 当前选择的回复风格
    this.currentReplyStyle = 'professional';

    // 当前选择的语言
    this.currentLanguage = 'zh';

    // 快捷模板数据
    this.templates = {
      summary: { name: '总结', icon: '📝', prompt: '请总结以下内容的要点：' },
      translate: { name: '翻译', icon: '🌐', prompt: '请将以下内容翻译为{language}：' },
      analyze: { name: '分析', icon: '🔍', prompt: '请分析以下内容：' },
      reply: { name: '回复', icon: '💬', prompt: '请以{style}风格回复：' },
      mindmap: { name: '思维导图', icon: '🧠', prompt: '请为以下内容创建思维导图：' }
    };

    // 连接状态
    this.connectionStatus = 'ready';

    // 功能模块状态（简化版）
    this.moduleStates = {
      notion: { connected: false },
      settings: { loaded: false },
      autoAnalysis: { enabled: true },
      cursorEnhance: { enabled: true }
    };

    // 性能监控
    this.performanceMetrics = {
      startTime: Date.now(),
      messageCount: 0
    };
  }

  /**
   * @function initializeElements - 初始化DOM元素引用
   * @description 获取并缓存重构后界面的关键DOM元素引用
   */
  initializeElements() {
    // 主容器和布局
    this.container = document.getElementById('ai-sidebar-container');
    this.header = this.container?.querySelector('.ai-sidebar__header');
    this.mainContent = this.container?.querySelector('.ai-sidebar__main');

    // 顶部栏元素
    this.settingsBtn = document.getElementById('ai-sidebar-settings-btn');

    // 快捷模板系统
    this.templatesBar = this.container?.querySelector('.ai-templates-bar');
    this.templatesScroll = this.container?.querySelector('.ai-templates-scroll');
    this.templatesMoreBtn = document.getElementById('ai-templates-more-btn');

    // 对话区域
    this.chatContainer = this.container?.querySelector('.ai-chat');
    this.chatMessages = document.getElementById('ai-chat-messages');
    this.chatInputArea = this.container?.querySelector('.ai-chat__input-area');

    // 输入控件
    this.replyStyleSelect = document.getElementById('ai-reply-style');
    this.languageSelect = document.getElementById('ai-language');
    this.chatInput = document.getElementById('ai-chat-input');
    this.chatInputWrapper = this.container?.querySelector('.ai-chat__input-wrapper');

    // 设置模态框
    this.settingsModal = document.getElementById('ai-settings-modal');
    this.settingsCloseBtn = document.getElementById('ai-settings-close-btn');
    this.settingsSaveBtn = document.getElementById('ai-settings-save-btn');
    this.settingsResetBtn = document.getElementById('ai-settings-reset-btn');

    // 验证关键元素是否存在
    this.validateElements();
  }

  /**
   * @function validateElements - 验证关键DOM元素
   * @description 检查重构后界面的关键元素是否正确加载
   */
  validateElements() {
    const requiredElements = {
      'container': this.container,
      'chatMessages': this.chatMessages,
      'chatInput': this.chatInput,
      'replyStyleSelect': this.replyStyleSelect,
      'languageSelect': this.languageSelect,
      'settingsBtn': this.settingsBtn
    };

    const missingElements = [];
    for (const [name, element] of Object.entries(requiredElements)) {
      if (!element) {
        missingElements.push(name);
      }
    }

    if (missingElements.length > 0) {
      console.warn('⚠️ 缺少关键DOM元素:', missingElements);
    } else {
      console.log('✅ 所有关键DOM元素已正确加载');
    }
  }

  /**
   * @function setupEventListeners - 设置事件监听器
   * @description 为重构后的界面元素添加事件监听器
   */
  setupEventListeners() {
    // 设置按钮事件
    this.setupSettingsEvents();

    // 快捷模板系统事件
    this.setupTemplateEvents();

    // 对话输入事件
    this.setupChatInputEvents();

    // 对话气泡交互事件
    this.setupChatBubbleEvents();

    // 全局键盘事件
    this.setupGlobalKeyboardEvents();

    console.log('✅ 事件监听器设置完成');
  }

  /**
   * @function setupSettingsEvents - 设置设置相关事件
   * @description 设置模态框和设置按钮的事件监听
   */
  setupSettingsEvents() {
    // 设置按钮点击事件
    if (this.settingsBtn) {
      this.settingsBtn.addEventListener('click', () => {
        this.openSettingsModal();
      });
    }

    // 设置模态框关闭事件
    if (this.settingsCloseBtn) {
      this.settingsCloseBtn.addEventListener('click', () => {
        this.closeSettingsModal();
      });
    }

    // 设置保存事件
    if (this.settingsSaveBtn) {
      this.settingsSaveBtn.addEventListener('click', () => {
        this.saveSettings();
      });
    }

    // 设置重置事件
    if (this.settingsResetBtn) {
      this.settingsResetBtn.addEventListener('click', () => {
        this.resetSettings();
      });
    }

    // 模态框背景点击关闭
    if (this.settingsModal) {
      this.settingsModal.addEventListener('click', (e) => {
        if (e.target === this.settingsModal) {
          this.closeSettingsModal();
        }
      });
    }
  }

  /**
   * @function setupTemplateEvents - 设置快捷模板事件
   * @description 设置模板按钮和更多按钮的事件监听
   */
  setupTemplateEvents() {
    // 模板按钮点击事件
    if (this.templatesScroll) {
      this.templatesScroll.addEventListener('click', (e) => {
        const templateItem = e.target.closest('.ai-template-item');
        if (templateItem) {
          const templateType = templateItem.dataset.template;
          this.useTemplate(templateType);
        }
      });
    }

    // 更多模板按钮事件
    if (this.templatesMoreBtn) {
      this.templatesMoreBtn.addEventListener('click', () => {
        this.showMoreTemplates();
      });
    }
  }

  /**
   * @function setupChatInputEvents - 设置对话输入事件
   * @description 设置输入框、选择器等输入相关事件
   */
  setupChatInputEvents() {
    // 输入框键盘事件
    if (this.chatInput) {
      this.chatInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          this.sendMessage();
        }
      });

      // 输入框自动调整高度
      this.chatInput.addEventListener('input', () => {
        this.adjustInputHeight();
      });
    }

    // 回复风格选择事件
    if (this.replyStyleSelect) {
      this.replyStyleSelect.addEventListener('change', (e) => {
        this.currentReplyStyle = e.target.value;
        console.log('回复风格已切换为:', this.currentReplyStyle);
      });
    }

    // 语言选择事件
    if (this.languageSelect) {
      this.languageSelect.addEventListener('change', (e) => {
        this.currentLanguage = e.target.value;
        console.log('语言已切换为:', this.currentLanguage);
      });
    }
  }

  /**
   * @function setupChatBubbleEvents - 设置对话气泡事件
   * @description 设置气泡悬浮菜单和操作按钮事件
   */
  setupChatBubbleEvents() {
    // 使用事件委托处理气泡操作按钮
    if (this.chatMessages) {
      this.chatMessages.addEventListener('click', (e) => {
        const actionBtn = e.target.closest('.ai-action-btn');
        if (actionBtn) {
          const action = actionBtn.dataset.action;
          const messageElement = actionBtn.closest('.ai-chat__message');
          this.handleBubbleAction(action, messageElement);
        }
      });
    }
  }

  /**
   * @function setupGlobalKeyboardEvents - 设置全局键盘事件
   * @description 设置全局快捷键和键盘事件
   */
  setupGlobalKeyboardEvents() {
    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        if (this.settingsModal && !this.settingsModal.classList.contains('hidden')) {
          this.closeSettingsModal();
        }
      }
    });
  }

  /**
   * @function initializeTemplateSystem - 初始化快捷模板系统
   * @description 初始化模板数据和模板按钮
   */
  initializeTemplateSystem() {
    // 渲染模板按钮
    this.renderTemplateButtons();

    // 加载用户自定义模板
    this.loadCustomTemplates();

    console.log('✅ 快捷模板系统初始化完成');
  }

  /**
   * @function initializeChatBubbleInteractions - 初始化对话气泡交互
   * @description 初始化气泡悬浮菜单和操作功能
   */
  initializeChatBubbleInteractions() {
    // 设置气泡操作处理器
    this.bubbleActionHandlers = {
      copy: this.copyMessageContent.bind(this),
      translate: this.translateMessage.bind(this),
      notion: this.saveToNotion.bind(this)
    };

    console.log('✅ 对话气泡交互初始化完成');
  }

  /**
   * @function renderTemplateButtons - 渲染模板按钮
   * @description 在模板栏中渲染快捷模板按钮
   */
  renderTemplateButtons() {
    if (!this.templatesScroll) return;

    const templateButtons = Object.entries(this.templates).map(([key, template]) => {
      return `
        <button type="button" class="ai-template-item" data-template="${key}">
          <span class="ai-template-icon">${template.icon}</span>
          <span class="ai-template-name">${template.name}</span>
        </button>
      `;
    }).join('');

    this.templatesScroll.innerHTML = templateButtons;
  }

  /**
   * @function loadCustomTemplates - 加载用户自定义模板
   * @description 从本地存储加载用户自定义的模板
   */
  loadCustomTemplates() {
    try {
      const customTemplates = localStorage.getItem('ai-custom-templates');
      if (customTemplates) {
        const parsed = JSON.parse(customTemplates);
        this.templates = { ...this.templates, ...parsed };
        this.renderTemplateButtons();
      }
    } catch (error) {
      console.warn('加载自定义模板失败:', error);
    }
  }

  /**
   * @function useTemplate - 使用模板
   * @description 将选中的模板内容插入到输入框
   * @param {string} templateType - 模板类型
   */
  useTemplate(templateType) {
    const template = this.templates[templateType];
    if (!template || !this.chatInput) return;

    let prompt = template.prompt;

    // 替换模板变量
    prompt = prompt.replace('{style}', this.getStyleText(this.currentReplyStyle));
    prompt = prompt.replace('{language}', this.getLanguageText(this.currentLanguage));

    // 插入到输入框
    this.chatInput.value = prompt;
    this.chatInput.focus();

    // 调整输入框高度
    this.adjustInputHeight();

    console.log(`使用模板: ${template.name}`);
  }

  /**
   * @function showMoreTemplates - 显示更多模板
   * @description 显示模板管理界面或更多模板选项
   */
  showMoreTemplates() {
    // 这里可以实现模板管理界面
    console.log('显示更多模板选项');
    // 暂时显示一个简单的提示
    this.showNotification('模板管理功能开发中...', 'info');
  }

  /**
   * @function sendMessage - 发送消息
   * @description 发送用户输入的消息并处理回复
   */
  async sendMessage() {
    const message = this.chatInput?.value?.trim();
    if (!message) return;

    // 清空输入框
    this.chatInput.value = '';
    this.adjustInputHeight();

    // 添加用户消息到界面
    this.addMessageToChat('user', message);

    try {
      // 根据选择的风格生成回复
      const response = await this.generateStyledResponse(message);

      // 添加AI回复到界面
      this.addMessageToChat('assistant', response);

    } catch (error) {
      console.error('发送消息失败:', error);
      this.addMessageToChat('assistant', '抱歉，处理您的消息时出现了错误。请稍后重试。');
    }
  }

  /**
   * @function addMessageToChat - 添加消息到对话界面
   * @description 在对话区域添加新的消息气泡
   * @param {string} type - 消息类型 ('user' | 'assistant')
   * @param {string} content - 消息内容
   */
  addMessageToChat(type, content) {
    if (!this.chatMessages) return;

    const messageId = 'msg-' + Date.now();
    const timestamp = new Date().toLocaleTimeString();

    const messageHtml = `
      <div class="ai-chat__message ai-chat__message--${type}" data-message-id="${messageId}">
        <div class="ai-chat__bubble">
          <div class="ai-chat__content">
            <div class="ai-chat__text">${this.escapeHtml(content)}</div>
          </div>
          <div class="ai-chat__actions">
            <button type="button" class="ai-action-btn" data-action="copy" title="复制">
              <span class="ai-icon">📋</span>
            </button>
            <button type="button" class="ai-action-btn" data-action="translate" title="翻译">
              <span class="ai-icon">🌐</span>
            </button>
            <button type="button" class="ai-action-btn" data-action="notion" title="保存到Notion">
              <span class="ai-icon">📝</span>
            </button>
          </div>
        </div>
        <div class="ai-chat__time">${timestamp}</div>
      </div>
    `;

    this.chatMessages.insertAdjacentHTML('beforeend', messageHtml);

    // 滚动到底部
    this.scrollToBottom();

    // 更新消息历史
    this.messageHistory.push({
      id: messageId,
      type,
      content,
      timestamp: Date.now()
    });
  }

  /**
   * @function generateStyledResponse - 生成风格化回复
   * @description 根据选择的风格和语言生成AI回复
   * @param {string} message - 用户消息
   * @returns {Promise<string>} AI回复
   */
  async generateStyledResponse(message) {
    // 构建带风格的提示词
    const stylePrompt = this.buildStylePrompt(message);

    // 发送到后台处理
    const response = await this.sendToBackground({
      type: MESSAGE_TYPES.CHAT_SEND,
      payload: {
        message: stylePrompt,
        style: this.currentReplyStyle,
        language: this.currentLanguage,
        history: this.messageHistory.slice(-5) // 只发送最近5条消息作为上下文
      }
    });

    if (response && response.success) {
      return response.data.reply || '收到您的消息，正在处理中...';
    } else {
      throw new Error(response?.error || '生成回复失败');
    }
  }

  /**
   * @function buildStylePrompt - 构建风格化提示词
   * @description 根据选择的风格构建提示词
   * @param {string} message - 原始消息
   * @returns {string} 风格化提示词
   */
  buildStylePrompt(message) {
    const styleInstructions = {
      professional: '请以专业、正式的语调回复',
      casual: '请以轻松、友好的语调回复',
      detailed: '请提供详细、全面的回复',
      concise: '请提供简洁、要点明确的回复',
      creative: '请以创意、有趣的方式回复'
    };

    const instruction = styleInstructions[this.currentReplyStyle] || styleInstructions.professional;

    return `${instruction}，使用${this.getLanguageText(this.currentLanguage)}回复：\n\n${message}`;
  }

  /**
   * @function handleBubbleAction - 处理气泡操作
   * @description 处理气泡悬浮菜单的操作按钮点击
   * @param {string} action - 操作类型
   * @param {HTMLElement} messageElement - 消息元素
   */
  handleBubbleAction(action, messageElement) {
    if (!messageElement) return;

    const messageId = messageElement.dataset.messageId;
    const textElement = messageElement.querySelector('.ai-chat__text');
    const content = textElement?.textContent || '';

    if (this.bubbleActionHandlers[action]) {
      this.bubbleActionHandlers[action](content, messageId, messageElement);
    } else {
      console.warn('未知的气泡操作:', action);
    }
  }

  /**
   * @function copyMessageContent - 复制消息内容
   * @description 复制消息内容到剪贴板
   * @param {string} content - 消息内容
   * @param {string} messageId - 消息ID
   */
  async copyMessageContent(content, messageId) {
    try {
      await navigator.clipboard.writeText(content);
      this.showNotification('内容已复制到剪贴板', 'success');
    } catch (error) {
      console.error('复制失败:', error);
      // 降级方案：使用传统方法
      this.fallbackCopy(content);
    }
  }

  /**
   * @function translateMessage - 翻译消息
   * @description 翻译消息内容
   * @param {string} content - 消息内容
   * @param {string} messageId - 消息ID
   */
  async translateMessage(content, messageId) {
    try {
      const targetLanguage = this.currentLanguage === 'zh' ? 'en' : 'zh';
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.I18N_TRANSLATE,
        payload: {
          text: content,
          targetLanguage,
          sourceLanguage: this.currentLanguage
        }
      });

      if (response && response.success) {
        // 在当前消息下方添加翻译结果
        this.addTranslationResult(messageId, response.data.translatedText, targetLanguage);
      } else {
        throw new Error(response?.error || '翻译失败');
      }
    } catch (error) {
      console.error('翻译失败:', error);
      this.showNotification('翻译失败，请稍后重试', 'error');
    }
  }

  /**
   * @function saveToNotion - 保存到Notion
   * @description 将消息内容保存到Notion
   * @param {string} content - 消息内容
   * @param {string} messageId - 消息ID
   */
  async saveToNotion(content, messageId) {
    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.NOTION_SYNC,
        payload: {
          content,
          messageId,
          timestamp: Date.now(),
          source: 'ai-sidebar-chat'
        }
      });

      if (response && response.success) {
        this.showNotification('已保存到Notion', 'success');
      } else {
        throw new Error(response?.error || '保存失败');
      }
    } catch (error) {
      console.error('保存到Notion失败:', error);
      this.showNotification('保存到Notion失败，请检查连接设置', 'error');
    }
  }

  /**
   * @function adjustInputHeight - 调整输入框高度
   * @description 根据内容自动调整输入框高度
   */
  adjustInputHeight() {
    if (!this.chatInput) return;

    this.chatInput.style.height = 'auto';
    const scrollHeight = this.chatInput.scrollHeight;
    const maxHeight = 120; // 最大高度120px

    this.chatInput.style.height = Math.min(scrollHeight, maxHeight) + 'px';
  }

  /**
   * @function scrollToBottom - 滚动到底部
   * @description 将对话区域滚动到最底部
   */
  scrollToBottom() {
    if (this.chatMessages) {
      this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

  /**
   * @function getStyleText - 获取风格文本
   * @description 获取风格的中文描述
   * @param {string} style - 风格代码
   * @returns {string} 风格描述
   */
  getStyleText(style) {
    const styleTexts = {
      professional: '专业',
      casual: '轻松',
      detailed: '详细',
      concise: '简洁',
      creative: '创意'
    };
    return styleTexts[style] || '专业';
  }

  /**
   * @function getLanguageText - 获取语言文本
   * @description 获取语言的中文描述
   * @param {string} language - 语言代码
   * @returns {string} 语言描述
   */
  getLanguageText(language) {
    const languageTexts = {
      zh: '中文',
      en: '英文',
      ja: '日文',
      ko: '韩文'
    };
    return languageTexts[language] || '中文';
  }

  /**
   * @function escapeHtml - 转义HTML
   * @description 转义HTML特殊字符，防止XSS
   * @param {string} text - 原始文本
   * @returns {string} 转义后的文本
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * @function fallbackCopy - 降级复制方法
   * @description 当现代剪贴板API不可用时的降级方案
   * @param {string} text - 要复制的文本
   */
  fallbackCopy(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';
    document.body.appendChild(textArea);
    textArea.select();

    try {
      document.execCommand('copy');
      this.showNotification('内容已复制到剪贴板', 'success');
    } catch (error) {
      this.showNotification('复制失败', 'error');
    } finally {
      document.body.removeChild(textArea);
    }
  }

  /**
   * @function addTranslationResult - 添加翻译结果
   * @description 在消息下方添加翻译结果
   * @param {string} messageId - 消息ID
   * @param {string} translatedText - 翻译文本
   * @param {string} targetLanguage - 目标语言
   */
  addTranslationResult(messageId, translatedText, targetLanguage) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!messageElement) return;

    // 检查是否已有翻译结果
    const existingTranslation = messageElement.querySelector('.ai-translation-result');
    if (existingTranslation) {
      existingTranslation.remove();
    }

    const languageText = this.getLanguageText(targetLanguage);
    const translationHtml = `
      <div class="ai-translation-result">
        <div class="ai-translation-label">翻译为${languageText}：</div>
        <div class="ai-translation-text">${this.escapeHtml(translatedText)}</div>
      </div>
    `;

    messageElement.insertAdjacentHTML('beforeend', translationHtml);
  }

  /**
   * @function openSettingsModal - 打开设置模态框
   * @description 显示设置模态框并加载当前设置
   */
  openSettingsModal() {
    if (this.settingsModal) {
      this.settingsModal.classList.remove('hidden');
      this.loadCurrentSettings();
    }
  }

  /**
   * @function closeSettingsModal - 关闭设置模态框
   * @description 隐藏设置模态框
   */
  closeSettingsModal() {
    if (this.settingsModal) {
      this.settingsModal.classList.add('hidden');
    }
  }

  /**
   * @function loadCurrentSettings - 加载当前设置
   * @description 从存储中加载设置并填充到表单
   */
  async loadCurrentSettings() {
    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.SETTINGS_GET
      });

      if (response && response.success) {
        const settings = response.data;
        this.populateSettingsForm(settings);
      }
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  }

  /**
   * @function populateSettingsForm - 填充设置表单
   * @description 将设置数据填充到表单控件
   * @param {Object} settings - 设置数据
   */
  populateSettingsForm(settings) {
    // API配置
    const apiKeyInput = document.getElementById('ai-api-key');
    const apiEndpointInput = document.getElementById('ai-api-endpoint');

    if (apiKeyInput && settings.apiKey) {
      apiKeyInput.value = settings.apiKey;
    }
    if (apiEndpointInput && settings.apiEndpoint) {
      apiEndpointInput.value = settings.apiEndpoint;
    }

    // Notion配置
    const notionTokenInput = document.getElementById('ai-notion-token');
    const notionDatabaseInput = document.getElementById('ai-notion-database');

    if (notionTokenInput && settings.notionToken) {
      notionTokenInput.value = settings.notionToken;
    }
    if (notionDatabaseInput && settings.notionDatabase) {
      notionDatabaseInput.value = settings.notionDatabase;
    }

    // 功能设置
    const autoAnalysisCheckbox = document.getElementById('ai-auto-analysis');
    const cursorEnhanceCheckbox = document.getElementById('ai-cursor-enhance');
    const notionSyncCheckbox = document.getElementById('ai-notion-sync');

    if (autoAnalysisCheckbox) {
      autoAnalysisCheckbox.checked = settings.autoAnalysis !== false;
    }
    if (cursorEnhanceCheckbox) {
      cursorEnhanceCheckbox.checked = settings.cursorEnhance !== false;
    }
    if (notionSyncCheckbox) {
      notionSyncCheckbox.checked = settings.notionSync !== false;
    }
  }

  /**
   * @function saveSettings - 保存设置
   * @description 收集表单数据并保存设置
   */
  async saveSettings() {
    try {
      const settings = this.collectSettingsData();

      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.SETTINGS_SET,
        payload: settings
      });

      if (response && response.success) {
        this.showNotification('设置已保存', 'success');
        this.closeSettingsModal();
      } else {
        throw new Error(response?.error || '保存失败');
      }
    } catch (error) {
      console.error('保存设置失败:', error);
      this.showNotification('保存设置失败', 'error');
    }
  }

  /**
   * @function collectSettingsData - 收集设置数据
   * @description 从表单控件收集设置数据
   * @returns {Object} 设置数据
   */
  collectSettingsData() {
    return {
      apiKey: document.getElementById('ai-api-key')?.value || '',
      apiEndpoint: document.getElementById('ai-api-endpoint')?.value || '',
      notionToken: document.getElementById('ai-notion-token')?.value || '',
      notionDatabase: document.getElementById('ai-notion-database')?.value || '',
      autoAnalysis: document.getElementById('ai-auto-analysis')?.checked || false,
      cursorEnhance: document.getElementById('ai-cursor-enhance')?.checked || false,
      notionSync: document.getElementById('ai-notion-sync')?.checked || false
    };
  }

  /**
   * @function resetSettings - 重置设置
   * @description 重置所有设置为默认值
   */
  async resetSettings() {
    if (!confirm('确定要重置所有设置吗？此操作不可撤销。')) {
      return;
    }

    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.SETTINGS_RESET
      });

      if (response && response.success) {
        this.showNotification('设置已重置', 'success');
        this.loadCurrentSettings();
      } else {
        throw new Error(response?.error || '重置失败');
      }
    } catch (error) {
      console.error('重置设置失败:', error);
      this.showNotification('重置设置失败', 'error');
    }
  }

  /**
   * @function setupMessageConnection - 建立消息连接
   * @description 设置与后台服务的消息通信
   */
  setupMessageConnection() {
    // 监听来自后台的消息
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        this.handleBackgroundMessage(message, sender, sendResponse);
      });
    }

    console.log('✅ 消息连接已建立');
  }

  /**
   * @function handleBackgroundMessage - 处理后台消息
   * @description 处理来自后台服务的消息
   * @param {Object} message - 消息对象
   * @param {Object} sender - 发送者信息
   * @param {Function} sendResponse - 响应函数
   */
  handleBackgroundMessage(message, sender, sendResponse) {
    const { type, payload } = message;

    switch (type) {
      case MESSAGE_TYPES.ANALYSIS_RESULT:
        this.handleAnalysisResult(payload);
        break;
      case MESSAGE_TYPES.NOTION_STATUS:
        this.handleNotionStatus(payload);
        break;
      case MESSAGE_TYPES.SETTINGS_UPDATE:
        this.handleSettingsUpdate(payload);
        break;
      default:
        console.log('收到未处理的后台消息:', type);
    }
  }

  /**
   * @function sendToBackground - 发送消息到后台
   * @description 向后台服务发送消息并等待响应
   * @param {Object} message - 消息对象
   * @returns {Promise<Object>} 响应对象
   */
  async sendToBackground(message) {
    return new Promise((resolve, reject) => {
      if (typeof chrome === 'undefined' || !chrome.runtime) {
        reject(new Error('Chrome Runtime API不可用'));
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('消息发送超时'));
      }, 10000);

      chrome.runtime.sendMessage(message, (response) => {
        clearTimeout(timeout);

        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }

  /**
   * @function initializeInterface - 初始化界面
   * @description 初始化界面状态和自动功能
   */
  async initializeInterface() {
    // 设置初始状态
    this.setInitialState();

    // 自动页面分析（如果启用）
    if (this.moduleStates.autoAnalysis.enabled) {
      this.autoAnalyzePage();
    }

    // 加载设置
    await this.loadCurrentSettings();

    console.log('✅ 界面初始化完成');
  }

  /**
   * @function setInitialState - 设置初始状态
   * @description 设置界面的初始状态
   */
  setInitialState() {
    // 设置默认选择器值
    if (this.replyStyleSelect) {
      this.replyStyleSelect.value = this.currentReplyStyle;
    }
    if (this.languageSelect) {
      this.languageSelect.value = this.currentLanguage;
    }

    // 聚焦输入框
    if (this.chatInput) {
      this.chatInput.focus();
    }
  }

  /**
   * @function autoAnalyzePage - 自动分析页面
   * @description 自动分析当前页面内容
   */
  async autoAnalyzePage() {
    try {
      const pageInfo = await this.getCurrentPageInfo();
      if (pageInfo && pageInfo.textContent) {
        // 生成页面摘要
        const summary = await this.generatePageSummary(pageInfo);
        if (summary) {
          this.addMessageToChat('assistant', `📄 页面分析完成：\n\n${summary}`);
        }
      }
    } catch (error) {
      console.error('自动页面分析失败:', error);
    }
  }

  /**
   * @function getCurrentPageInfo - 获取当前页面信息
   * @description 获取当前页面的标题、URL和内容
   * @returns {Promise<Object>} 页面信息
   */
  async getCurrentPageInfo() {
    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.CONTENT_CAPTURED,
        payload: {
          url: window.location.href,
          title: document.title,
          textContent: document.body.innerText.substring(0, 5000) // 限制长度
        }
      });

      if (response && response.success) {
        return response.data;
      }
    } catch (error) {
      console.error('获取页面信息失败:', error);
    }

    return {
      url: window.location.href,
      title: document.title,
      textContent: document.body.innerText.substring(0, 5000)
    };
  }

  /**
   * @function generatePageSummary - 生成页面摘要
   * @description 生成当前页面的智能摘要
   * @param {Object} pageInfo - 页面信息
   * @returns {Promise<string>} 页面摘要
   */
  async generatePageSummary(pageInfo) {
    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.CONTENT_SUMMARIZE,
        payload: {
          content: pageInfo.textContent,
          title: pageInfo.title,
          url: pageInfo.url
        }
      });

      if (response && response.success) {
        return response.data.summary;
      }
    } catch (error) {
      console.error('生成页面摘要失败:', error);
    }

    return null;
  }

  /**
   * @function showNotification - 显示通知
   * @description 显示临时通知消息
   * @param {string} message - 通知消息
   * @param {string} type - 通知类型 ('success' | 'error' | 'info' | 'warning')
   */
  showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `ai-notification ai-notification--${type}`;
    notification.textContent = message;

    // 添加到页面
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
      notification.classList.add('ai-notification--show');
    }, 10);

    // 自动隐藏
    setTimeout(() => {
      notification.classList.remove('ai-notification--show');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  /**
   * @function handleAnalysisResult - 处理分析结果
   * @description 处理来自后台的分析结果
   * @param {Object} payload - 分析结果数据
   */
  handleAnalysisResult(payload) {
    if (payload && payload.summary) {
      this.addMessageToChat('assistant', `📊 页面分析结果：\n\n${payload.summary}`);
    }
  }

  /**
   * @function handleNotionStatus - 处理Notion状态
   * @description 处理Notion连接状态更新
   * @param {Object} payload - 状态数据
   */
  handleNotionStatus(payload) {
    if (payload && payload.connected !== undefined) {
      this.moduleStates.notion.connected = payload.connected;
      const status = payload.connected ? '已连接' : '未连接';
      console.log(`Notion状态: ${status}`);
    }
  }

  /**
   * @function handleSettingsUpdate - 处理设置更新
   * @description 处理设置更新通知
   * @param {Object} payload - 设置数据
   */
  handleSettingsUpdate(payload) {
    console.log('设置已更新:', payload);
    this.showNotification('设置已更新', 'info');
  }
}

// 初始化侧边栏面板
document.addEventListener('DOMContentLoaded', () => {
  try {
    window.aiSidebarPanel = new AiSidebarPanel();
    console.log('✅ AI侧边栏面板已成功初始化');
  } catch (error) {
    console.error('❌ AI侧边栏面板初始化失败:', error);
  }
});

// 导出类以供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AiSidebarPanel;
}










      targetPanel.classList.add('ai-sidebar__panel--active');
      
      this.activeMainTab = tabName;
      
      // 触发标签页激活事件
      this.onTabActivated(tabName);
      
      console.log(`🔄 切换到主标签页: ${tabName}`);
    } else {
      console.warn(`⚠️ 无法找到标签页: ${tabName}`);
    }
  }
  
  /**
   * @function onTabActivated - 标签页激活回调
   * @param {string} tabName - 激活的标签页名称
   * @description 处理标签页激活时的特定逻辑
   */
  onTabActivated(tabName) {
    try {
      console.log(`[标签页] 激活标签页: ${tabName}`);

      switch (tabName) {
        case 'chat':
          if (typeof this.onChatTabActivated === 'function') {
            this.onChatTabActivated();
          } else {
            console.error('[标签页] onChatTabActivated 方法不存在');
          }
          break;
        case 'settings':
          if (typeof this.onSettingsTabActivated === 'function') {
            this.onSettingsTabActivated();
          } else {
            console.error('[标签页] onSettingsTabActivated 方法不存在');
          }
          break;
        case 'notion':
          if (typeof this.onNotionTabActivated === 'function') {
            this.onNotionTabActivated();
          } else {
            console.error('[标签页] onNotionTabActivated 方法不存在');
          }
          break;
        case 'analysis':
          if (typeof this.onAnalysisTabActivated === 'function') {
            this.onAnalysisTabActivated();
          } else {
            console.error('[标签页] onAnalysisTabActivated 方法不存在');
          }
          break;
        case 'enhance':
          if (typeof this.onEnhanceTabActivated === 'function') {
            this.onEnhanceTabActivated();
          } else {
            console.error('[标签页] onEnhanceTabActivated 方法不存在');
          }
          break;
        default:
          console.warn(`[标签页] 未知的标签页类型: ${tabName}`);
      }
    } catch (error) {
      console.error(`[标签页] 激活标签页失败 (${tabName}):`, error);
    }
  }
  
  /**
   * @function updateConnectionStatus - 更新连接状态
   * @param {string} status - 连接状态 ('ready' | 'connecting' | 'connected' | 'disconnected' | 'error')
   * @description 更新顶部连接状态指示器
   */
  updateConnectionStatus(status) {
    const dotEl = this.statusElements?.connection;
    if (!dotEl) return;

    // 移除旧的状态类
    dotEl.classList.remove(
      'ai-sidebar__status-dot--connected',
      'ai-sidebar__status-dot--connecting',
      'ai-sidebar__status-dot--disconnected'
    );

    // 根据状态添加对应的样式
    switch (status) {
      case 'ready':
      case 'connected':
        dotEl.classList.add('ai-sidebar__status-dot--connected');
        break;
      case 'connecting':
        dotEl.classList.add('ai-sidebar__status-dot--connecting');
        break;
      case 'disconnected':
      case 'error':
        dotEl.classList.add('ai-sidebar__status-dot--disconnected');
        break;
    }

    // 记录当前连接状态
    this.connectionStatusState = status;
  }
  
  /**
   * @function updateStatusBar - 更新状态栏
   * @param {string} message - 状态消息
   * @description 更新底部状态栏显示的消息
   */
  updateStatusBar(message) {
    if (this.statusElements?.footer) {
      this.statusElements.footer.textContent = message;
    }
  }
  
  /**
   * @function showNotification - 显示通知
   * @param {string} message - 通知消息
   * @param {string} type - 通知类型 ('info' | 'success' | 'warning' | 'error')
   * @param {number} duration - 显示时长(毫秒)，默认3000
   * @description 显示顶部通知消息
   */
  showNotification(message, type = 'info', duration = 3000) {
    const notification = {
      id: Date.now(),
      message,
      type,
      timestamp: new Date()
    };
    
    this.notifications.unshift(notification);
    
    // 更新通知按钮徽章
    this.updateNotificationBadge();
    
    // 如果是错误类型，持续显示
    if (type !== 'error' && duration > 0) {
      setTimeout(() => {
        this.removeNotification(notification.id);
      }, duration);
    }
    
    console.log(`📢 通知 [${type.toUpperCase()}]: ${message}`);
  }
  
  /**
   * @function updateNotificationBadge - 更新通知徽章
   * @description 更新通知按钮上的未读徽章数量
   */
  updateNotificationBadge() {
    const badge = this.notificationsBtn?.querySelector('.ai-badge');
    const unreadCount = this.notifications.length;
    
    if (badge) {
      if (unreadCount > 0) {
        badge.textContent = unreadCount > 99 ? '99+' : unreadCount.toString();
        badge.style.display = 'flex';
      } else {
        badge.style.display = 'none';
      }
    }
  }

  /**
   * @function handleSendMessage - 处理发送消息
   * @description 发送用户输入的消息到AI服务
   */
  async handleSendMessage() {
    const messageText = this.chatInput?.value.trim();
    if (!messageText) return;
    
    // 显示用户消息
    this.addMessage(messageText, 'user');
    
    // 清空输入框
    this.chatInput.value = '';
    this.updateSendButton();
    
    // 显示加载状态
    this.showLoading();
    
    try {
      // 发送消息到background处理
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.CHAT_STREAM,
        payload: {
          text: messageText,
          history: this.messageHistory.slice(-10) // 只发送最近10条消息作为上下文
        }
      });
      
      if (response.success) {
        // 显示AI回复
        this.addMessage(response.reply, 'assistant');
      } else {
        this.addMessage('抱歉，发生了错误，请稍后重试。', 'assistant');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      this.addMessage('网络连接出现问题，请检查连接后重试。', 'assistant');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * @function addMessage - 添加消息到对话列表
   * @param {string} text - 消息文本
   * @param {string} type - 消息类型 ('user' | 'assistant')
   * @description 在对话界面中添加新消息
   */
  addMessage(text, type) {
    const messageElement = document.createElement('div');
    messageElement.className = `ai-chat__message ai-chat__message--${type}`;
    
    messageElement.innerHTML = `
      <div class="ai-chat__message-content">
        <div class="ai-chat__message-text">${this.escapeHtml(text)}</div>
      </div>
    `;
    
    this.messagesContainer?.appendChild(messageElement);
    
    // 记录到历史
    this.messageHistory.push({
      text,
      type,
      timestamp: Date.now()
    });
    
    // 滚动到底部
    this.scrollToBottom();
  }

  /**
   * @function handleInputKeydown - 处理输入框按键事件
   * @param {KeyboardEvent} event - 键盘事件
   * @description 处理Enter发送和其他快捷键
   */
  handleInputKeydown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.handleSendMessage();
    }
  }

  /**
   * @function handleInputChange - 处理输入框内容变化
   * @description 更新发送按钮状态
   */
  handleInputChange() {
    this.updateSendButton();
    const text = this.chatInput.value.trim();
    if (text.length > 2) {
      // 实际应调用API获取建议
      this.chatElements.suggestions.classList.remove('hidden');
    } else {
      this.chatElements.suggestions.classList.add('hidden');
    }
  }

  /**
   * @function updateSendButton - 更新发送按钮状态
   * @description 根据输入框内容启用/禁用发送按钮
   */
  updateSendButton() {
    const hasText = this.chatInput?.value.trim().length > 0;
    if (this.sendBtn) {
      this.sendBtn.disabled = !hasText;
    }
  }

  /**
   * @function handleAnalyzePage - 处理页面分析请求
   * @description 触发当前页面的智能分析
   */
  async handleAnalyzePage() {
    // 切换到分析面板，使用已有的 switchMainTab 方法
    this.switchMainTab('analysis');
    this.handleStartAnalysis();
  }

  /**
   * @function handleStartAnalysis - 开始页面分析
   * @description 启动页面内容分析流程
   */
  async handleStartAnalysis() {
    this.analysisStatus = 'analyzing';
    this.showLoading();
    
    try {
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.ANALYSIS_REQUEST,
        payload: {
          pageInfo: await this.getCurrentPageInfo(),
          options: this.getAnalysisOptions()
        }
      });
      
      if (response.success) {
        this.displayAnalysisResults(response.analysis);
      } else {
        this.displayAnalysisError('分析失败，请稍后重试');
      }
    } catch (error) {
      console.error('页面分析失败:', error);
      this.showNotification('页面分析失败', 'error');
      this.updateStatusIndicator('分析失败');
    } finally {
      this.hideLoading();
      // 分析流程结束，重置状态
      this.analysisStatus = 'idle';
    }
  }

  /**
   * @function displayAnalysisResults - 显示分析结果
   * @param {Object} analysis - 分析结果数据
   * @description 在分析面板中显示页面分析结果
   */
  displayAnalysisResults(analysis) {
    if (!this.analysisResults) return;
    
    this.analysisResults.innerHTML = `
      <div class="ai-analysis__result">
        <h4>页面分析结果</h4>
        <div class="ai-analysis__summary">
          <p><strong>页面标题:</strong> ${this.escapeHtml(analysis.title || '未知')}</p>
          <p><strong>内容类型:</strong> ${this.escapeHtml(analysis.contentType || '网页')}</p>
          <p><strong>字数统计:</strong> ${analysis.wordCount || 0} 字</p>
        </div>
        ${analysis.summary ? `
          <div class="ai-analysis__content">
            <h5>内容摘要:</h5>
            <p>${this.escapeHtml(analysis.summary)}</p>
          </div>
        ` : ''}
        ${analysis.keyPoints && analysis.keyPoints.length > 0 ? `
          <div class="ai-analysis__points">
            <h5>关键信息:</h5>
            <ul>
              ${analysis.keyPoints.map(point => `<li>${this.escapeHtml(point)}</li>`).join('')}
            </ul>
          </div>
        ` : ''}
      </div>
    `;
  }

  /**
   * @function handleSmartReply - 处理智能回复功能
   * @description 基于页面内容生成智能回复建议
   */
  async handleSmartReply() {
    console.log('智能回复功能待实现');
    try {
      this.sendToBackground({
        type: MESSAGE_TYPES.SMART_REPLY_GENERATE,
        payload: {
          pageContent: '...' // 省略，应从内容脚本获取
        }
      });
    } catch (error) {
      console.error('智能回复生成失败:', error);
      this.addMessage('智能回复功能暂时不可用。', 'assistant');
    }
  }

  /**
   * @function showSettingsModal - 显示设置模态框
   * @description 打开设置配置界面
   */
  showSettingsModal() {
    console.log('显示设置模态框');
    if (this.settingsModal) {
      this.settingsModal.classList.remove('hidden');
      this.loadCurrentSettings(); // 打开时加载最新设置
    }
  }

  /**
   * @function setupSettingsModalEvents - 设置模态框事件
   * @description 为模态框添加关闭和交互事件
   */
  setupSettingsModalEvents() {
    const modal = document.getElementById('ai-settings-modal');
    const closeBtn = document.getElementById('ai-settings-modal-close');
    const overlay = modal?.querySelector('.ai-modal__overlay');
    
    // 关闭按钮事件
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hideSettingsModal();
      });
    }
    
    // 点击遮罩关闭
    if (overlay) {
      overlay.addEventListener('click', () => {
        this.hideSettingsModal();
      });
    }
    
    // ESC键关闭
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && modal && modal.style.display !== 'none') {
        this.hideSettingsModal();
      }
    });
    
    // 设置导航切换
    this.setupSettingsNavigation();
    
    // 设置表单控件事件
    this.setupSettingsFormEvents();
    
    // 设置操作按钮事件
    this.setupSettingsActionEvents();
  }

  /**
   * @function setupSettingsNavigation - 设置导航切换
   * @description 设置设置面板的导航切换逻辑
   */
  setupSettingsNavigation() {
    const navItems = document.querySelectorAll('.ai-settings-nav__item');
    const panels = document.querySelectorAll('.ai-settings-panel');
    
    navItems.forEach(item => {
      item.addEventListener('click', () => {
        const tabId = item.dataset.settingsTab;
        
        // 更新导航状态
        navItems.forEach(nav => nav.classList.remove('ai-settings-nav__item--active'));
        item.classList.add('ai-settings-nav__item--active');
        
        // 切换面板
        panels.forEach(panel => {
          panel.classList.remove('ai-settings-panel--active');
          if (panel.dataset.settingsPanel === tabId) {
            panel.classList.add('ai-settings-panel--active');
          }
        });
        
        // 记录当前选中的设置面板
        this.moduleStates.settings.activePanel = tabId;
      });
    });
  }

  /**
   * @function setupSettingsFormEvents - 设置表单控件事件
   * @description 设置设置面板的表单控件事件
   */
  setupSettingsFormEvents() {
    // 开关控件事件
    this.setupSwitchEvents();
    
    // 范围滑块事件
    this.setupRangeEvents();
    
    // 输入框事件
    this.setupInputEvents();
    
    // 选择框事件
    this.setupSelectEvents();
  }

  /**
   * @function setupSwitchEvents - 设置开关控件事件
   * @description 设置设置面板的开关控件事件
   */
  setupSwitchEvents() {
    const switches = document.querySelectorAll('.ai-switch input[type="checkbox"]');
    
    switches.forEach(switchEl => {
      switchEl.addEventListener('change', (e) => {
        const setting = e.target.id.replace('ai-setting-', '');
        const value = e.target.checked;
        
        // 保存设置到本地存储
        this.saveSetting(setting, value);
        
        // 特殊处理某些设置
        this.handleSpecialSettings(setting, value);
        
        console.log(`[设置] ${setting}: ${value}`);
      });
    });
  }

  /**
   * @function setupRangeEvents - 设置范围滑块事件
   * @description 设置设置面板的范围滑块事件
   */
  setupRangeEvents() {
    const ranges = document.querySelectorAll('.ai-range');
    
    ranges.forEach(range => {
      // 实时更新显示值
      range.addEventListener('input', (e) => {
        const valueDisplay = e.target.nextElementSibling;
        if (valueDisplay && valueDisplay.classList.contains('ai-settings__range-value')) {
          let displayValue = e.target.value;
          
          // 根据设置类型格式化显示值
          if (e.target.id.includes('memory') || e.target.id.includes('cache')) {
            displayValue += 'MB';
          } else if (e.target.id.includes('width')) {
            displayValue += 'px';
          }
          
          valueDisplay.textContent = displayValue;
        }
      });
      
      // 保存设置值
      range.addEventListener('change', (e) => {
        const setting = e.target.id.replace('ai-setting-', '');
        const value = parseInt(e.target.value);
        
        this.saveSetting(setting, value);
        console.log(`[设置] ${setting}: ${value}`);
      });
    });
  }

  /**
   * @function setupInputEvents - 设置输入框事件
   * @description 设置设置面板的输入框事件
   */
  setupInputEvents() {
    const inputs = document.querySelectorAll('.ai-input');
    
    inputs.forEach(input => {
      // 密码框显示/隐藏切换
      if (input.type === 'password') {
        this.addPasswordToggle(input);
      }
      
      // 保存输入值
      input.addEventListener('blur', (e) => {
        const setting = e.target.id.replace('ai-setting-', '');
        let value = e.target.value;
        
        // 数字类型转换
        if (e.target.type === 'number') {
          value = parseInt(value) || 0;
        }
        
        this.saveSetting(setting, value);
        console.log(`[设置] ${setting}: ${value}`);
      });
      
      // 实时验证
      input.addEventListener('input', (e) => {
        this.validateInput(e.target);
      });
    });
  }

  /**
   * @function setupSelectEvents - 设置选择框事件
   * @description 设置设置面板的选择框事件
   */
  setupSelectEvents() {
    const selects = document.querySelectorAll('.ai-select');
    
    selects.forEach(select => {
      select.addEventListener('change', (e) => {
        const setting = e.target.id.replace('ai-setting-', '');
        const value = e.target.value;
        
        this.saveSetting(setting, value);
        
        // 特殊处理某些设置
        this.handleSpecialSettings(setting, value);
        
        console.log(`[设置] ${setting}: ${value}`);
      });
    });
  }

  /**
   * @function setupSettingsActionEvents - 设置操作按钮事件
   * @description 设置设置面板的操作按钮事件
   */
  setupSettingsActionEvents() {
    // 主面板上的按钮
    this.toolbarElements.settings.refreshBtn?.addEventListener('click', () => this.loadCurrentSettings());
    this.toolbarElements.settings.exportBtn?.addEventListener('click', () => this.exportSettings());
    this.toolbarElements.settings.importBtn?.addEventListener('click', () => this.importSettings());
    
    // 模态框底部的按钮
    document.getElementById('ai-settings-reset-btn')?.addEventListener('click', () => this.resetAllSettings());
    document.getElementById('ai-settings-export-btn-modal')?.addEventListener('click', () => this.exportSettings());
    document.getElementById('ai-settings-import-btn-modal')?.addEventListener('click', () => this.importSettings());
    document.getElementById('ai-settings-save-btn')?.addEventListener('click', () => this.saveAllSettings());

    // 主面板上的保存/重置
    document.getElementById('ai-settings-reset-all')?.addEventListener('click', () => this.resetAllSettings());
    document.getElementById('ai-settings-save-all')?.addEventListener('click', () => this.saveAllSettings());
  }

  /**
   * @function hideSettingsModal - 隐藏设置模态框
   * @description 隐藏设置配置界面
   */
  hideSettingsModal() {
    if (this.settingsModal) {
      this.settingsModal.classList.add('hidden');
    }
  }

  /**
   * @function loadCurrentSettings - 加载当前设置值到表单
   * @description 从设置管理器加载当前设置值到表单
   */
  async loadCurrentSettings() {
    try {
      // 从设置管理器获取所有设置
      if (this.settingsManager) {
        const allSettings = await this.settingsManager.getAllSettings();
        
        // 填充表单控件
        Object.entries(allSettings).forEach(([key, value]) => {
          const element = document.getElementById(`ai-setting-${key}`);
          if (element) {
            if (element.type === 'checkbox') {
              element.checked = value;
            } else if (element.type === 'range') {
              element.value = value;
              // 更新显示值
              const valueDisplay = element.nextElementSibling;
              if (valueDisplay) {
                let displayValue = value;
                if (key.includes('memory') || key.includes('cache')) {
                  displayValue += 'MB';
                } else if (key.includes('width')) {
                  displayValue += 'px';
                }
                valueDisplay.textContent = displayValue;
              }
            } else {
              element.value = value;
            }
          }
        });
      }
      
      // API密钥已硬编码，无需从UI加载
    } catch (error) {
      console.error('[设置] 加载设置失败:', error);
      this.showNotification('加载设置失败', 'error');
    }
  }

  /**
   * @function saveSetting - 保存单个设置
   * @param {string} key - 设置键
   * @param {*} value - 设置值
   * @description 将设置保存到设置管理器
   */
  async saveSetting(key, value) {
    this.sendToBackground({
      type: MESSAGE_TYPES.SETTINGS_SET,
      payload: { key, value },
    });
  }

  /**
   * @function saveAllSettings - 保存所有设置
   * @description 将所有设置保存到设置管理器
   */
  async saveAllSettings() {
    try {
      // 显示全局加载指示器
      this.showLoading();
      
      // 收集所有表单数据
      const formData = this.collectFormData();
      
      // 验证设置
      const validation = this.validateSettings(formData);
      if (!validation.valid) {
        this.hideLoading();
        this.showNotification(validation.message, 'error');
        return;
      }
      
      // 保存设置到设置管理器（API密钥已硬编码，无需处理）
      if (this.settingsManager) {
        await this.settingsManager.updateSettings(formData);
      }
      
      this.hideLoading();
      this.showNotification('设置保存成功', 'success');
      
      console.log('[设置] 所有设置已保存');
    } catch (error) {
      this.hideLoading();
      console.error('[设置] 保存所有设置失败:', error);
      this.showNotification('保存设置失败', 'error');
    }
  }

  /**
   * @function resetAllSettings - 重置所有设置
   * @description 将所有设置重置到默认值
   */
  async resetAllSettings() {
    try {
      // 显示全局加载指示器
      this.showLoading();
      
      if (this.settingsManager) {
        await this.settingsManager.resetSettings();
      }
      
      // 重新加载设置到表单
      await this.loadCurrentSettings();
      
      this.hideLoading();
      this.showNotification('设置已重置', 'success');
      
      console.log('[设置] 所有设置已重置');
    } catch (error) {
      this.hideLoading();
      console.error('[设置] 重置设置失败:', error);
      this.showNotification('重置设置失败', 'error');
    }
  }

  /**
   * @function exportSettings - 导出设置
   * @description 将设置导出为JSON文件
   */
  async exportSettings() {
    console.log('导出设置');
    this.showLoading();
    this.sendToBackground({ type: MESSAGE_TYPES.SETTINGS_EXPORT });
  }

  /**
   * @function importSettings - 导入设置
   * @description 从JSON文件导入设置
   */
  async importSettings() {
    try {
      // 创建文件选择器
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      
      input.onchange = async (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = async (e) => {
            try {
              const settingsData = e.target.result;
              
              if (this.settingsManager) {
                await this.settingsManager.importSettings(settingsData);
                await this.loadCurrentSettings();
                this.showNotification('设置导入成功', 'success');
                console.log('[设置] 设置已导入');
              }
            } catch (error) {
              console.error('[设置] 导入设置失败:', error);
              this.showNotification('导入设置失败：文件格式无效', 'error');
            }
          };
          reader.readAsText(file);
        }
      };
      
      input.click();
    } catch (error) {
      console.error('[设置] 导入设置失败:', error);
      this.showNotification('导入设置失败', 'error');
    }
  }

  /**
   * @function collectFormData - 收集表单数据
   * @returns {Object} 表单数据
   * @description 收集所有设置输入
   */
  collectFormData() {
    const formData = {};
    
    // 收集所有设置输入
    const inputs = document.querySelectorAll('[id^="ai-setting-"]');
    inputs.forEach(input => {
      const key = input.id.replace('ai-setting-', '');
      let value = input.value;
      
      if (input.type === 'checkbox') {
        value = input.checked;
      } else if (input.type === 'number' || input.type === 'range') {
        value = parseInt(value) || 0;
      }
      
      formData[key] = value;
    });
    
    return formData;
  }

  /**
   * @function validateSettings - 验证设置数据
   * @param {Object} settings - 设置数据
   * @returns {Object} 验证结果
   * @description 验证设置数据
   */
  validateSettings(settings) {
    // API密钥已硬编码，无需验证
    
    // 数值范围验证
    if (settings['memory-limit'] && (settings['memory-limit'] < 20 || settings['memory-limit'] > 100)) {
      return { valid: false, message: '内存限制必须在20-100MB之间' };
    }
    
    if (settings['api-timeout'] && (settings['api-timeout'] < 5 || settings['api-timeout'] > 120)) {
      return { valid: false, message: 'API超时时间必须在5-120秒之间' };
    }
    
    return { valid: true };
  }

  /**
   * @function handleSpecialSettings - 处理特殊设置
   * @param {string} setting - 设置名称
   * @param {*} value - 设置值
   * @description 处理特殊设置逻辑
   */
  handleSpecialSettings(setting, value) {
    switch (setting) {
      case 'theme':
        this.applyTheme(value);
        break;
      case 'language':
        this.applyLanguage(value);
        break;
      case 'notion-sync':
        if (value && this.notionConnector) {
          this.notionConnector.connect();
        }
        break;
      case 'auto-sync':
        if (this.notionConnector) {
          if (value) {
            this.notionConnector.startAutoSync();
          } else {
            this.notionConnector.stopAutoSync();
          }
        }
        break;
    }
  }

  /**
   * @function applyTheme - 应用主题
   * @param {string} theme - 主题名称
   * @description 应用主题逻辑
   */
  applyTheme(theme) {
    const root = document.documentElement;
    
    if (theme === 'dark') {
      root.classList.add('ai-theme-dark');
    } else if (theme === 'light') {
      root.classList.remove('ai-theme-dark');
    } else {
      // 跟随系统
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (prefersDark) {
        root.classList.add('ai-theme-dark');
      } else {
        root.classList.remove('ai-theme-dark');
      }
    }
  }

  /**
   * @function applyLanguage - 应用语言设置
   * @param {string} language - 语言代码
   * @description 应用语言设置逻辑
   */
  applyLanguage(language) {
    // 这里可以添加国际化逻辑
    console.log(`[设置] 切换语言到: ${language}`);
  }

  /**
   * @function addPasswordToggle - 添加密码显示切换
   * @param {HTMLInputElement} input - 密码输入框
   * @description 添加密码显示切换逻辑
   */
  addPasswordToggle(input) {
    const wrapper = document.createElement('div');
    wrapper.className = 'ai-password-wrapper';
    
    const toggleBtn = document.createElement('button');
    toggleBtn.type = 'button';
    toggleBtn.className = 'ai-password-toggle';
    toggleBtn.innerHTML = '👁️';
    toggleBtn.title = '显示/隐藏密码';
    
    input.parentNode.insertBefore(wrapper, input);
    wrapper.appendChild(input);
    wrapper.appendChild(toggleBtn);
    
    toggleBtn.addEventListener('click', () => {
      if (input.type === 'password') {
        input.type = 'text';
        toggleBtn.innerHTML = '🙈';
      } else {
        input.type = 'password';
        toggleBtn.innerHTML = '👁️';
      }
    });
  }

  /**
   * @function validateInput - 验证输入框
   * @param {HTMLInputElement} input - 输入框元素
   * @description 验证输入框逻辑
   */
  validateInput(input) {
    let isValid = true;
    let message = '';
    
    // 移除之前的错误样式
    input.classList.remove('ai-input--error');
    
    // API密钥已硬编码，无需验证
    
    // 应用验证结果
    if (!isValid) {
      input.classList.add('ai-input--error');
      input.title = message;
    } else {
      input.title = '';
    }
    
    return isValid;
  }

  /**
   * @function showLoading - 显示加载指示器
   * @description 显示操作进行中的加载状态
   */
  showLoading() {
    if (this.loadingIndicator) {
      this.loadingIndicator.classList.remove('hidden');
    }
  }

  /**
   * @function hideLoading - 隐藏加载指示器
   * @description 隐藏加载状态指示器
   */
  hideLoading() {
    if (this.loadingIndicator) {
      this.loadingIndicator.classList.add('hidden');
    }
  }

  /**
   * @function updateStatusIndicator - 更新状态指示器
   * @param {string} status - 状态文本
   * @description 更新底部状态栏显示内容
   */
  updateStatusIndicator(status) {
    if (this.statusElements?.footer) {
      this.statusElements.footer.textContent = status;
    }
  }

  /**
   * @function sendToBackground - 发送消息到background脚本
   * @param {Object} message - 要发送的消息对象
   * @returns {Promise<Object>} 返回响应Promise
   * @description 与background脚本进行异步通信
   */
  sendToBackground(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  }

  /**
   * @function handleBackgroundMessage - 处理来自background的消息
   * @param {Object} message - 接收到的消息
   * @param {Function} sendResponse - 响应函数
   * @description 处理background脚本发送的消息
   */
  handleBackgroundMessage(message, sendResponse) {
    console.log('Sidebar received message:', message);
    const { type, payload } = message;

    switch (type) {
      case MESSAGE_TYPES.CHAT_STREAM:
        this.handleStreamingResponse(payload);
        break;
      case MESSAGE_TYPES.ANALYSIS_RESULT:
        this.displayAnalysisResults(payload);
        this.hideLoading();
        break;
      case MESSAGE_TYPES.SETTINGS_UPDATE:
        this.loadCurrentSettings();
        this.showNotification('设置已在后台更新', 'info');
        break;
      case MESSAGE_TYPES.NOTION_STATUS:
        this.updateNotionStatus(payload.status, payload.data);
        this.hideLoading();
        break;
      case MESSAGE_TYPES.NOTIFICATION_SHOW:
        this.showNotification(payload.message, payload.type);
        break;
    }
  }

  /**
   * @function escapeHtml - HTML转义
   * @param {string} text - 要转义的文本
   * @returns {string} 转义后的安全HTML文本
   * @description 防止XSS攻击的HTML转义处理
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * @function scrollToBottom - 滚动到消息底部
   * @description 将消息列表滚动到最新消息
   */
  scrollToBottom() {
    if (this.messagesContainer) {
      this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }
  }

  /**
   * @function handleResize - 处理窗口大小变化
   * @description 响应式布局调整
   */
  handleResize() {
    // 可以在这里添加响应式布局调整逻辑
    console.log('窗口大小已改变');
  }

  // #region 分析面板功能方法

  /**
   * @function setupAnalysisControlEvents - 设置分析控制事件
   * @description 处理分析选项和深度设置
   */
  setupAnalysisControlEvents() {
    // 分析标签页切换
    const analysisTabs = document.querySelectorAll('.ai-analysis__tab');
    const analysisContents = document.querySelectorAll('.ai-analysis__content');
    
    analysisTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const targetTab = tab.dataset.analysisTab;
        
        // 更新标签页状态
        analysisTabs.forEach(t => t.classList.remove('ai-analysis__tab--active'));
        tab.classList.add('ai-analysis__tab--active');
        
        // 更新内容显示
        analysisContents.forEach(content => {
          if (content.dataset.analysisContent === targetTab) {
            content.classList.add('ai-analysis__content--active');
          } else {
            content.classList.remove('ai-analysis__content--active');
          }
        });
        
        // 处理特殊标签页
        this.onAnalysisTabActivated(targetTab);
      });
    });

    // 分析选项复选框
    const analysisOptions = document.querySelectorAll('.ai-analysis__options input[type="checkbox"]');
    analysisOptions.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.updateAnalysisOptions();
      });
    });
    
    // 分析深度选择
    const depthSelect = document.getElementById('ai-analysis-depth');
    if (depthSelect) {
      depthSelect.addEventListener('change', () => {
        this.updateAnalysisDepth(depthSelect.value);
      });
    }

    // 对比分析事件
    const compareBtn = document.getElementById('ai-analysis-compare-btn');
    if (compareBtn) {
      compareBtn.addEventListener('click', () => {
        this.handleCompareAnalysis();
      });
    }

    // 趋势分析事件
    const trendRefreshBtn = document.getElementById('ai-trend-refresh-btn');
    if (trendRefreshBtn) {
      trendRefreshBtn.addEventListener('click', () => {
        this.refreshTrendAnalysis();
      });
    }

    // 历史记录清空
    const historyClearBtn = document.getElementById('ai-history-clear-btn');
    if (historyClearBtn) {
      historyClearBtn.addEventListener('click', () => {
        this.clearAnalysisHistory();
      });
    }
  }

  /**
   * @function startPageAnalysis - 开始页面分析
   * @description 启动当前页面的智能分析
   */
  async startPageAnalysis() {
    try {
      this.showLoading();
      this.updateStatusIndicator('正在分析页面...');

      // 获取分析选项
      const options = this.getAnalysisOptions();
      
      // 获取页面信息
      const pageInfo = await this.getCurrentPageInfo();
      
      // 显示页面信息卡片
      this.displayPageInfo(pageInfo);
      
      // 执行分析
      const analysisResult = await this.performPageAnalysis(pageInfo, options);
      
      // 显示分析结果
      this.displayDetailedAnalysisResults(analysisResult);
      
      // 保存到历史记录
      this.saveAnalysisToHistory(analysisResult);
      
      this.updateStatusIndicator('页面分析完成');
      this.showNotification('页面分析完成', 'success');
      
    } catch (error) {
      console.error('页面分析失败:', error);
      this.showNotification('页面分析失败', 'error');
      this.updateStatusIndicator('分析失败');
    } finally {
      this.hideLoading();
      // 分析流程结束，重置状态
      this.analysisStatus = 'idle';
    }
  }

  /**
   * @function getAnalysisOptions - 获取分析选项
   * @returns {Object} 分析选项配置
   */
  getAnalysisOptions() {
    return {
      text: document.getElementById('ai-analysis-text')?.checked || false,
      images: document.getElementById('ai-analysis-images')?.checked || false,
      links: document.getElementById('ai-analysis-links')?.checked || false,
      seo: document.getElementById('ai-analysis-seo')?.checked || false,
      depth: document.getElementById('ai-analysis-depth')?.value || 'standard'
    };
  }

  /**
   * @function getCurrentPageInfo - 获取当前页面信息
   * @returns {Promise<Object>} 页面信息对象
   */
  async getCurrentPageInfo() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // 注入内容脚本获取页面详细信息
      const pageData = await chrome.tabs.sendMessage(tab.id, {
        type: 'GET_PAGE_INFO'
      });

      return {
        title: tab.title || pageData?.title || '无标题',
        url: tab.url || '',
        wordCount: pageData?.wordCount || 0,
        readingTime: Math.ceil((pageData?.wordCount || 0) / 200),
        lastUpdated: new Date().toLocaleString(),
        content: pageData?.content || '',
        images: pageData?.images || [],
        links: pageData?.links || [],
        headings: pageData?.headings || {}
      };
    } catch (error) {
      console.error('获取页面信息失败:', error);
      return {
        title: '无法获取页面信息',
        url: window.location.href,
        wordCount: 0,
        readingTime: 0,
        lastUpdated: new Date().toLocaleString()
      };
    }
  }

  /**
   * @function displayPageInfo - 显示页面信息
   * @param {Object} pageInfo - 页面信息对象
   */
  displayPageInfo(pageInfo) {
    if (!pageInfo || !this.pageInfoContainer) return;

    this.pageInfoContainer.classList.remove('hidden');
    
    const titleEl = this.pageInfoContainer.querySelector('#ai-page-title');
    const urlEl = this.pageInfoContainer.querySelector('#ai-page-url');
    const wordCountEl = this.pageInfoContainer.querySelector('#ai-page-word-count');
    const readingTimeEl = this.pageInfoContainer.querySelector('#ai-page-reading-time');
    const lastUpdatedEl = this.pageInfoContainer.querySelector('#ai-page-last-updated');

    if (titleEl) titleEl.textContent = pageInfo.title;
    if (urlEl) urlEl.textContent = pageInfo.url;
    if (wordCountEl) wordCountEl.textContent = `${pageInfo.wordCount} 字`;
    if (readingTimeEl) readingTimeEl.textContent = `${pageInfo.readingTime} 分钟阅读`;
    if (lastUpdatedEl) lastUpdatedEl.textContent = pageInfo.lastUpdated;
  }

  /**
   * @function performPageAnalysis - 执行页面分析
   * @param {Object} pageInfo - 页面信息
   * @param {Object} options - 分析选项
   * @returns {Promise<Object>} 分析结果
   */
  async performPageAnalysis(pageInfo, options) {
    // 模拟分析过程（实际项目中会调用AI API）
    await new Promise(resolve => setTimeout(resolve, 2000));

    const result = {
      timestamp: Date.now(),
      pageInfo,
      options,
      content: {
        summary: this.generateContentSummary(pageInfo.content),
        keywords: this.extractKeywords(pageInfo.content),
        score: Math.floor(Math.random() * 20) + 80
      },
      structure: {
        headings: pageInfo.headings,
        paragraphs: this.countParagraphs(pageInfo.content),
        images: pageInfo.images.length,
        links: pageInfo.links.length,
        score: Math.floor(Math.random() * 30) + 70
      },
      seo: this.analyzeSEO(pageInfo),
      suggestions: this.generateSuggestions(pageInfo)
    };

    return result;
  }

  /**
   * @function displayDetailedAnalysisResults - 显示详细分析结果
   * @param {Object} result - 分析结果
   */
  displayDetailedAnalysisResults(result) {
    const resultsContainer = document.getElementById('ai-analysis-results');
    const template = document.getElementById('ai-analysis-template');
    
    if (!resultsContainer || !template) return;

    // 克隆模板
    const resultElement = template.cloneNode(true);
    resultElement.id = '';
    resultElement.style.display = 'block';

    // 填充内容摘要
    this.fillContentSummary(resultElement, result.content);
    
    // 填充结构分析
    this.fillStructureAnalysis(resultElement, result.structure);
    
    // 填充SEO分析
    this.fillSEOAnalysis(resultElement, result.seo);
    
    // 填充智能建议
    this.fillSuggestions(resultElement, result.suggestions);

    // 替换占位符内容
    resultsContainer.innerHTML = '';
    resultsContainer.appendChild(resultElement);
  }

  /**
   * @function fillContentSummary - 填充内容摘要
   * @param {HTMLElement} element - 结果元素
   * @param {Object} content - 内容分析结果
   */
  fillContentSummary(element, content) {
    const scoreElement = element.querySelector('#ai-score-content');
    const summaryElement = element.querySelector('#ai-content-summary');
    const keywordsElement = element.querySelector('#ai-content-keywords .ai-keywords__list');

    if (scoreElement) {
      scoreElement.textContent = content.score;
      scoreElement.setAttribute('data-score', content.score >= 85 ? 'high' : content.score >= 70 ? 'medium' : 'low');
    }

    if (summaryElement) {
      summaryElement.textContent = content.summary;
    }

    if (keywordsElement) {
      keywordsElement.innerHTML = content.keywords.map(keyword => 
        `<span class="ai-keyword-tag">${this.escapeHtml(keyword)}</span>`
      ).join('');
    }
  }

  /**
   * @function fillStructureAnalysis - 填充结构分析
   * @param {HTMLElement} element - 结果元素
   * @param {Object} structure - 结构分析结果
   */
  fillStructureAnalysis(element, structure) {
    const scoreElement = element.querySelector('#ai-score-structure');
    const headingsElement = element.querySelector('#ai-metric-headings');
    const paragraphsElement = element.querySelector('#ai-metric-paragraphs');
    const imagesElement = element.querySelector('#ai-metric-images');
    const linksElement = element.querySelector('#ai-metric-links');

    if (scoreElement) {
      scoreElement.textContent = structure.score;
      scoreElement.setAttribute('data-score', structure.score >= 85 ? 'high' : structure.score >= 70 ? 'medium' : 'low');
    }

    if (headingsElement) {
      const headingText = Object.entries(structure.headings)
        .map(([level, count]) => `${level}(${count})`)
        .join(' ');
      headingsElement.textContent = headingText || '无标题';
    }

    if (paragraphsElement) paragraphsElement.textContent = structure.paragraphs;
    if (imagesElement) imagesElement.textContent = structure.images;
    if (linksElement) linksElement.textContent = structure.links;
  }

  /**
   * @function onAnalysisTabActivated - 分析标签页激活回调
   * @param {string} tabName - 激活的标签页名称
   */
  onAnalysisTabActivated(tabName) {
    switch (tabName) {
      case 'current':
        // 当前页面分析
        break;
      case 'compare':
        this.loadComparablePages();
        break;
      case 'trend':
        this.loadTrendData();
        break;
      case 'history':
        this.loadAnalysisHistory();
        break;
    }
  }

  // #endregion

  // #region 增强面板功能方法

  /**
   * @function toggleCursorEnhancement - 切换光标增强
   * @description 启用或禁用光标增强功能
   */
  async toggleCursorEnhancement() {
    try {
      const isEnabled = await this.getCursorEnhancementStatus();
      const newStatus = !isEnabled;
      
      await this.setCursorEnhancementStatus(newStatus);
      this.updateCursorEnhancementUI(newStatus);
      
      const message = newStatus ? '光标增强已启用' : '光标增强已禁用';
      this.showNotification(message, 'success');
      
    } catch (error) {
      console.error('切换光标增强失败:', error);
      this.showNotification('操作失败', 'error');
    }
  }

  /**
   * @function updateCursorEnhancementUI - 更新光标增强UI状态
   * @param {boolean} isEnabled - 是否启用
   */
  updateCursorEnhancementUI(isEnabled) {
    const statusCard = document.getElementById('ai-cursor-status-card');
    const toggleBtn = document.getElementById('ai-enhance-toggle-btn');
    
    if (statusCard) {
      const icon = statusCard.querySelector('.ai-status-card__icon .ai-icon');
      const title = statusCard.querySelector('.ai-status-card__title');
      const text = statusCard.querySelector('.ai-status-card__text');
      
      if (isEnabled) {
        if (icon) icon.textContent = '✅';
        if (title) title.textContent = '光标增强已启用';
        if (text) text.textContent = '智能预测和自动补全功能正在运行';
      } else {
        if (icon) icon.textContent = '❌';
        if (title) title.textContent = '光标增强已禁用';
        if (text) text.textContent = '启用后可获得智能输入预测和自动补全功能';
      }
    }
    
    if (toggleBtn) {
      const btnText = toggleBtn.querySelector('span:last-child');
      if (btnText) {
        btnText.textContent = isEnabled ? '禁用增强' : '启用增强';
      }
    }
  }

  /**
   * @function setupEnhanceTabEvents - 设置增强标签页事件
   * @description 处理增强面板内的标签页切换
   */
  setupEnhanceTabEvents() {
    const enhanceTabs = document.querySelectorAll('.ai-enhance__tab');
    const enhanceContents = document.querySelectorAll('.ai-enhance__content');
    
    enhanceTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const targetTab = tab.dataset.enhanceTab;
        
        // 更新标签页状态
        enhanceTabs.forEach(t => t.classList.remove('ai-enhance__tab--active'));
        tab.classList.add('ai-enhance__tab--active');
        
        // 更新内容显示
        enhanceContents.forEach(content => {
          if (content.dataset.enhanceContent === targetTab) {
            content.classList.add('ai-enhance__content--active');
          } else {
            content.classList.remove('ai-enhance__content--active');
          }
        });
        
        // 处理特殊标签页
        this.onEnhanceTabActivated(targetTab);
      });
    });
  }

  /**
   * @function setupTemplateEvents - 设置模板事件
   * @description 处理模板管理功能
   */
  setupTemplateEvents() {
    // 模板分类筛选
    const categorySelect = document.getElementById('ai-templates-category');
    if (categorySelect) {
      categorySelect.addEventListener('change', () => {
        this.filterTemplates(categorySelect.value);
      });
    }

    // 模板搜索
    const searchInput = document.getElementById('ai-templates-search');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        this.searchTemplates(searchInput.value);
      });
    }

    // 添加模板按钮
    const addBtn = document.getElementById('ai-templates-add-btn');
    if (addBtn) {
      addBtn.addEventListener('click', () => {
        this.showAddTemplateDialog();
      });
    }

    // 模板卡片事件
    this.setupTemplateCardEvents();
  }

  /**
   * @function setupTemplateCardEvents - 设置模板卡片事件
   * @description 处理模板卡片的交互事件
   */
  setupTemplateCardEvents() {
    const templateCards = document.querySelectorAll('.ai-template-card');
    
    templateCards.forEach(card => {
      const useBtn = card.querySelector('.ai-template-card__btn[title="使用模板"]');
      const editBtn = card.querySelector('.ai-template-card__btn[title="编辑模板"]');
      
      if (useBtn) {
        useBtn.addEventListener('click', () => {
          const templateId = card.dataset.templateId;
          this.useTemplate(templateId);
        });
      }
      
      if (editBtn) {
        editBtn.addEventListener('click', () => {
          const templateId = card.dataset.templateId;
          this.editTemplate(templateId);
        });
      }
    });
  }

  /**
   * @function onEnhanceTabActivated - 增强标签页激活回调
   * @param {string} tabName - 激活的标签页名称
   */
  onEnhanceTabActivated(tabName) {
    switch (tabName) {
      case 'cursor':
        this.loadCursorEnhancementStatus();
        break;
      case 'shortcuts':
        // 快捷键设置已静态显示
        break;
      case 'templates':
        this.loadTemplates();
        break;
      case 'stats':
        this.loadEnhancementStats();
        break;
    }
  }

  /**
   * @function loadEnhancementStats - 加载增强功能统计
   * @description 显示使用统计数据
   */
  async loadEnhancementStats() {
    try {
      // 模拟加载统计数据
      const stats = {
        suggestions: Math.floor(Math.random() * 1000) + 500,
        accepted: Math.floor(Math.random() * 500) + 200,
        accuracy: Math.floor(Math.random() * 20) + 75,
        timeSaved: Math.floor(Math.random() * 3600) + 1800
      };

      // 更新统计显示
      this.updateStatsDisplay(stats);
      
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  }

  /**
   * @function updateStatsDisplay - 更新统计显示
   * @param {Object} stats - 统计数据
   */
  updateStatsDisplay(stats) {
    const suggestionsElement = document.getElementById('ai-stats-suggestions');
    const acceptedElement = document.getElementById('ai-stats-accepted');
    const accuracyElement = document.getElementById('ai-stats-accuracy');
    const timeSavedElement = document.getElementById('ai-stats-time-saved');

    if (suggestionsElement) suggestionsElement.textContent = stats.suggestions;
    if (acceptedElement) acceptedElement.textContent = stats.accepted;
    if (accuracyElement) accuracyElement.textContent = `${stats.accuracy}%`;
    if (timeSavedElement) {
      const hours = Math.floor(stats.timeSaved / 3600);
      const minutes = Math.floor((stats.timeSaved % 3600) / 60);
      timeSavedElement.textContent = `${hours}h ${minutes}m`;
    }
  }

  // #endregion

  // #region 工具方法

  /**
   * @function generateContentSummary - 生成内容摘要
   * @param {string} content - 页面内容
   * @returns {string} 内容摘要
   */
  generateContentSummary(content) {
    if (!content || content.length < 100) {
      return '页面内容较少，无法生成有效摘要。';
    }
    
    // 简单的摘要生成逻辑（实际项目中会使用AI）
    const sentences = content.split(/[.!?。！？]/);
    const summary = sentences.slice(0, 3).join('。') + '。';
    return summary.length > 200 ? summary.substring(0, 200) + '...' : summary;
  }

  /**
   * @function extractKeywords - 提取关键词
   * @param {string} content - 页面内容
   * @returns {Array<string>} 关键词数组
   */
  extractKeywords(content) {
    // 简单的关键词提取逻辑
    const words = content.toLowerCase().match(/[\u4e00-\u9fa5\w]+/g) || [];
    const wordFreq = {};
    
    words.forEach(word => {
      if (word.length > 1) {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      }
    });
    
    return Object.entries(wordFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 8)
      .map(([word]) => word);
  }

  /**
   * @function countParagraphs - 统计段落数量
   * @param {string} content - 页面内容
   * @returns {number} 段落数量
   */
  countParagraphs(content) {
    return (content.match(/\n\s*\n/g) || []).length + 1;
  }

  /**
   * @function analyzeSEO - 分析SEO
   * @param {Object} pageInfo - 页面信息
   * @returns {Object} SEO分析结果
   */
  analyzeSEO(pageInfo) {
    const items = [];
    let score = 0;
    
    // 标题检查
    if (pageInfo.title && pageInfo.title.length > 0) {
      items.push({ icon: '✅', text: '页面标题存在', status: 'pass' });
      score += 20;
    } else {
      items.push({ icon: '❌', text: '缺少页面标题', status: 'fail' });
    }
    
    // 内容长度检查
    if (pageInfo.wordCount > 300) {
      items.push({ icon: '✅', text: '内容长度充足', status: 'pass' });
      score += 20;
    } else {
      items.push({ icon: '⚠️', text: '内容长度不足', status: 'warning' });
      score += 10;
    }
    
    // 图片检查
    if (pageInfo.images && pageInfo.images.length > 0) {
      items.push({ icon: '✅', text: '包含图片内容', status: 'pass' });
      score += 15;
    } else {
      items.push({ icon: '⚠️', text: '缺少图片内容', status: 'warning' });
      score += 5;
    }
    
    // 链接检查
    if (pageInfo.links && pageInfo.links.length > 0) {
      items.push({ icon: '✅', text: '包含外部链接', status: 'pass' });
      score += 15;
    } else {
      items.push({ icon: '⚠️', text: '缺少外部链接', status: 'warning' });
      score += 5;
    }
    
    return { items, score: Math.min(score, 100) };
  }

  /**
   * @function generateSuggestions - 生成智能建议
   * @param {Object} pageInfo - 页面信息
   * @returns {Array<Object>} 建议列表
   */
  generateSuggestions(pageInfo) {
    const suggestions = [];
    
    if (pageInfo.wordCount < 300) {
      suggestions.push({
        icon: '📝',
        title: '增加内容长度',
        desc: '建议将页面内容扩展至300字以上，以提高搜索引擎友好度。',
        priority: 'high'
      });
    }
    
    if (!pageInfo.images || pageInfo.images.length === 0) {
      suggestions.push({
        icon: '🖼️',
        title: '添加相关图片',
        desc: '适当添加相关图片可以提升用户体验和页面吸引力。',
        priority: 'medium'
      });
    }
    
    suggestions.push({
      icon: '🔍',
      title: '优化关键词密度',
      desc: '合理使用关键词，避免过度堆砌，保持自然的内容流畅度。',
      priority: 'low'
    });
    
    return suggestions;
  }

  /**
   * @function fillSEOAnalysis - 填充SEO分析
   * @param {HTMLElement} element - 结果元素
   * @param {Object} seo - SEO分析结果
   */
  fillSEOAnalysis(element, seo) {
    const scoreElement = element.querySelector('#ai-score-seo');
    const itemsContainer = element.querySelector('#ai-seo-items');

    if (scoreElement) {
      scoreElement.textContent = seo.score;
      scoreElement.setAttribute('data-score', seo.score >= 85 ? 'high' : seo.score >= 70 ? 'medium' : 'low');
    }

    if (itemsContainer && seo.items) {
      itemsContainer.innerHTML = seo.items.map(item => 
        `<div class="ai-seo-item ai-seo-item--${item.status}">
          <span class="ai-seo-item__icon">${item.icon}</span>
          <span class="ai-seo-item__text">${this.escapeHtml(item.text)}</span>
        </div>`
      ).join('');
    }
  }

  /**
   * @function fillSuggestions - 填充智能建议
   * @param {HTMLElement} element - 结果元素
   * @param {Array} suggestions - 建议列表
   */
  fillSuggestions(element, suggestions) {
    const suggestionsContainer = element.querySelector('#ai-suggestions-list');

    if (suggestionsContainer && suggestions) {
      suggestionsContainer.innerHTML = suggestions.map(suggestion => 
        `<div class="ai-suggestion ai-suggestion--${suggestion.priority}">
          <div class="ai-suggestion__icon">${suggestion.icon}</div>
          <div class="ai-suggestion__content">
            <div class="ai-suggestion__title">${this.escapeHtml(suggestion.title)}</div>
            <div class="ai-suggestion__desc">${this.escapeHtml(suggestion.desc)}</div>
          </div>
        </div>`
      ).join('');
    }
  }

  /**
   * @function saveAnalysisToHistory - 保存分析结果到历史记录
   * @param {Object} result - 分析结果
   */
  saveAnalysisToHistory(result) {
    try {
      const history = JSON.parse(localStorage.getItem('ai-analysis-history') || '[]');
      history.unshift({
        id: `analysis_${result.timestamp}`,
        timestamp: result.timestamp,
        url: result.pageInfo.url,
        title: result.pageInfo.title,
        summary: result.content.summary.substring(0, 100) + '...',
        scores: {
          content: result.content.score,
          structure: result.structure.score,
          seo: result.seo.score
        }
      });
      
      // 只保留最近50条记录
      if (history.length > 50) {
        history.splice(50);
      }
      
      localStorage.setItem('ai-analysis-history', JSON.stringify(history));
    } catch (error) {
      console.error('保存分析历史失败:', error);
    }
  }

  /**
   * @function updateAnalysisOptions - 更新分析选项
   * @description 响应分析选项变化
   */
  updateAnalysisOptions() {
    const options = this.getAnalysisOptions();
    console.log('分析选项已更新:', options);
    
    // 可以在这里添加选项变化的响应逻辑
    if (Object.values(options).some(enabled => enabled)) {
      this.updateStatusIndicator('分析选项已配置');
    } else {
      this.updateStatusIndicator('请选择至少一个分析选项');
    }
  }

  /**
   * @function updateAnalysisDepth - 更新分析深度
   * @param {string} depth - 分析深度
   */
  updateAnalysisDepth(depth) {
    console.log('分析深度已更新:', depth);
    this.updateStatusIndicator(`分析深度: ${depth}`);
  }

  /**
   * @function handleCompareAnalysis - 处理对比分析
   * @description 启动页面对比分析功能
   */
  async handleCompareAnalysis() {
    this.showNotification('对比分析功能开发中...', 'info');
    console.log('启动对比分析');
  }

  /**
   * @function refreshTrendAnalysis - 刷新趋势分析
   * @description 刷新趋势分析数据
   */
  async refreshTrendAnalysis() {
    this.showNotification('趋势分析功能开发中...', 'info');
    console.log('刷新趋势分析');
  }

  /**
   * @function clearAnalysisHistory - 清空分析历史
   * @description 清空所有分析历史记录
   */
  clearAnalysisHistory() {
    if (confirm('确定要清空所有分析历史记录吗？此操作不可撤销。')) {
      localStorage.removeItem('ai-analysis-history');
      this.showNotification('分析历史已清空', 'success');
      console.log('分析历史已清空');
    }
  }

  /**
   * @function loadComparablePages - 加载可对比页面
   * @description 加载可用于对比分析的页面列表
   */
  async loadComparablePages() {
    try {
      console.log('加载可对比页面列表');

      // 从本地存储获取历史页面数据
      const historyPages = JSON.parse(localStorage.getItem('ai-page-history') || '[]');

      // 模拟可对比页面数据
      const comparablePages = [
        {
          id: 'page-1',
          title: '当前页面',
          url: window.location.href,
          lastAnalyzed: new Date().toLocaleString(),
          score: 85
        },
        ...historyPages.slice(0, 5).map((page, index) => ({
          id: `page-${index + 2}`,
          title: page.title || '历史页面',
          url: page.url || '',
          lastAnalyzed: page.lastAnalyzed || '未知',
          score: Math.floor(Math.random() * 40) + 60
        }))
      ];

      // 更新对比页面列表UI
      const compareList = document.getElementById('ai-compare-pages-list');
      if (compareList) {
        compareList.innerHTML = comparablePages.map(page => `
          <div class="ai-compare-page-item" data-page-id="${page.id}">
            <div class="ai-page-info">
              <h4 class="ai-page-title">${page.title}</h4>
              <p class="ai-page-url">${page.url}</p>
              <span class="ai-page-date">分析时间: ${page.lastAnalyzed}</span>
            </div>
            <div class="ai-page-score">
              <span class="ai-score-value">${page.score}</span>
              <span class="ai-score-label">分</span>
            </div>
            <div class="ai-page-actions">
              <input type="checkbox" class="ai-compare-checkbox" value="${page.id}">
              <label>选择对比</label>
            </div>
          </div>
        `).join('');
      }

    } catch (error) {
      console.error('加载可对比页面失败:', error);
      this.showNotification('加载可对比页面失败', 'error');
    }
  }

  /**
   * @function loadTrendData - 加载趋势数据
   * @description 加载趋势分析数据
   */
  async loadTrendData() {
    try {
      console.log('加载趋势分析数据');

      // 模拟趋势数据
      const trendData = {
        timeRange: '最近30天',
        metrics: {
          pageViews: {
            current: 1250,
            previous: 980,
            change: '+27.6%',
            trend: 'up'
          },
          avgScore: {
            current: 82.5,
            previous: 78.2,
            change: '+5.5%',
            trend: 'up'
          },
          loadTime: {
            current: 2.3,
            previous: 2.8,
            change: '-17.9%',
            trend: 'down'
          },
          seoScore: {
            current: 88,
            previous: 85,
            change: '****%',
            trend: 'up'
          }
        },
        chartData: Array.from({length: 30}, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
          score: Math.floor(Math.random() * 20) + 70,
          views: Math.floor(Math.random() * 100) + 20
        }))
      };

      // 更新趋势数据UI
      const trendContainer = document.getElementById('ai-trend-data-container');
      if (trendContainer) {
        trendContainer.innerHTML = `
          <div class="ai-trend-summary">
            <h3>趋势概览 (${trendData.timeRange})</h3>
            <div class="ai-trend-metrics">
              ${Object.entries(trendData.metrics).map(([key, metric]) => `
                <div class="ai-metric-card">
                  <div class="ai-metric-label">${this.getMetricLabel(key)}</div>
                  <div class="ai-metric-value">${metric.current}</div>
                  <div class="ai-metric-change ai-trend--${metric.trend}">
                    ${metric.change}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          <div class="ai-trend-chart">
            <canvas id="ai-trend-chart" width="400" height="200"></canvas>
          </div>
        `;
      }

      // 绘制简单的趋势图表（这里可以集成Chart.js等图表库）
      this.drawTrendChart(trendData.chartData);

    } catch (error) {
      console.error('加载趋势数据失败:', error);
      this.showNotification('加载趋势数据失败', 'error');
    }
  }

  /**
   * @function getMetricLabel - 获取指标标签
   * @param {string} key - 指标键
   * @returns {string} 指标标签
   */
  getMetricLabel(key) {
    const labels = {
      pageViews: '页面浏览量',
      avgScore: '平均评分',
      loadTime: '加载时间(s)',
      seoScore: 'SEO评分'
    };
    return labels[key] || key;
  }

  /**
   * @function drawTrendChart - 绘制趋势图表
   * @param {Array} data - 图表数据
   */
  drawTrendChart(data) {
    try {
      const canvas = document.getElementById('ai-trend-chart');
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      const width = canvas.width;
      const height = canvas.height;

      // 清空画布
      ctx.clearRect(0, 0, width, height);

      // 绘制简单的折线图
      ctx.strokeStyle = '#3b82f6';
      ctx.lineWidth = 2;
      ctx.beginPath();

      data.forEach((point, index) => {
        const x = (index / (data.length - 1)) * width;
        const y = height - ((point.score - 50) / 50) * height;

        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();

      // 添加数据点
      ctx.fillStyle = '#3b82f6';
      data.forEach((point, index) => {
        const x = (index / (data.length - 1)) * width;
        const y = height - ((point.score - 50) / 50) * height;

        ctx.beginPath();
        ctx.arc(x, y, 3, 0, 2 * Math.PI);
        ctx.fill();
      });

    } catch (error) {
      console.error('绘制趋势图表失败:', error);
    }
  }

  /**
   * @function loadAnalysisHistory - 加载分析历史
   * @description 加载并显示分析历史记录
   */
  loadAnalysisHistory() {
    try {
      const history = JSON.parse(localStorage.getItem('ai-analysis-history') || '[]');
      console.log('加载分析历史:', history.length, '条记录');
      // 在这里可以更新UI显示历史记录
    } catch (error) {
      console.error('加载分析历史失败:', error);
    }
  }

  /**
   * @function getCursorEnhancementStatus - 获取光标增强状态
   * @returns {Promise<boolean>} 是否启用光标增强
   */
  async getCursorEnhancementStatus() {
    const status = await this.sendToBackground({
      type: MESSAGE_TYPES.CURSOR_ENHANCE_STATUS,
    });
    return status?.enabled;
  }

  /**
   * @function setCursorEnhancementStatus - 设置光标增强状态
   * @param {boolean} enabled - 是否启用
   */
  async setCursorEnhancementStatus(enabled) {
    this.sendToBackground({
      type: MESSAGE_TYPES.CURSOR_ENHANCE_TOGGLE,
      payload: { enabled },
    });
  }

  /**
   * @function filterTemplates - 筛选模板
   * @param {string} category - 模板分类
   */
  filterTemplates(category) {
    const templateCards = document.querySelectorAll('.ai-template-card');
    
    templateCards.forEach(card => {
      const cardCategory = card.dataset.category;
      if (category === 'all' || cardCategory === category) {
        card.style.display = 'block';
      } else {
        card.style.display = 'none';
      }
    });
    
    console.log('模板筛选:', category);
  }

  /**
   * @function searchTemplates - 搜索模板
   * @param {string} query - 搜索关键词
   */
  searchTemplates(query) {
    const templateCards = document.querySelectorAll('.ai-template-card');
    const searchTerm = query.toLowerCase();
    
    templateCards.forEach(card => {
      const title = card.querySelector('.ai-template-card__title')?.textContent.toLowerCase() || '';
      const preview = card.querySelector('.ai-template-card__preview')?.textContent.toLowerCase() || '';
      
      if (title.includes(searchTerm) || preview.includes(searchTerm)) {
        card.style.display = 'block';
      } else {
        card.style.display = 'none';
      }
    });
    
    console.log('模板搜索:', query);
  }

  /**
   * @function showAddTemplateDialog - 显示添加模板对话框
   * @description 显示创建新模板的对话框
   */
  showAddTemplateDialog() {
    this.showNotification('模板创建功能开发中...', 'info');
    console.log('显示添加模板对话框');
  }

  /**
   * @function useTemplate - 使用模板
   * @param {string} templateId - 模板ID
   */
  useTemplate(templateId) {
    this.showNotification(`正在使用模板: ${templateId}`, 'success');
    console.log('使用模板:', templateId);
  }

  /**
   * @function editTemplate - 编辑模板
   * @param {string} templateId - 模板ID
   */
  editTemplate(templateId) {
    this.showNotification(`正在编辑模板: ${templateId}`, 'info');
    console.log('编辑模板:', templateId);
  }

  /**
   * @function loadTemplates - 加载模板列表
   * @description 加载并显示模板库
   */
  async loadTemplates() {
    try {
      console.log('加载模板列表');

      // 模拟模板数据
      const templates = [
        {
          id: 'template-1',
          name: '邮件回复模板',
          category: 'email',
          content: '感谢您的邮件。我会尽快回复您的问题。',
          usage: 45
        },
        {
          id: 'template-2',
          name: '会议总结模板',
          category: 'meeting',
          content: '会议时间：\n参与人员：\n主要议题：\n决议事项：',
          usage: 32
        },
        {
          id: 'template-3',
          name: '代码注释模板',
          category: 'code',
          content: '/**\n * @function \n * @description \n * @param {} \n * @returns \n */',
          usage: 28
        }
      ];

      // 更新模板列表UI
      const templatesList = document.getElementById('ai-templates-list');
      if (templatesList) {
        templatesList.innerHTML = templates.map(template => `
          <div class="ai-template-card" data-template-id="${template.id}">
            <div class="ai-template-header">
              <h4 class="ai-template-name">${template.name}</h4>
              <span class="ai-template-usage">${template.usage}次使用</span>
            </div>
            <div class="ai-template-content">${template.content}</div>
            <div class="ai-template-actions">
              <button class="ai-btn ai-btn--sm" onclick="window.aiSidebarPanel.useTemplate('${template.id}')">使用</button>
              <button class="ai-btn ai-btn--sm ai-btn--secondary" onclick="window.aiSidebarPanel.editTemplate('${template.id}')">编辑</button>
            </div>
          </div>
        `).join('');
      }

    } catch (error) {
      console.error('加载模板失败:', error);
      this.showNotification('加载模板失败', 'error');
    }
  }

  /**
   * @function loadCursorEnhancementStatus - 加载光标增强状态
   * @description 加载并显示光标增强功能的当前状态
   */
  async loadCursorEnhancementStatus() {
    try {
      console.log('加载光标增强状态');

      // 从后台获取光标增强状态
      const response = await this.sendToBackground({
        type: MESSAGE_TYPES.CURSOR_ENHANCE_STATUS
      });

      if (response && response.success) {
        const status = response.data;

        // 更新UI显示
        const statusElement = document.getElementById('ai-cursor-status');
        const toggleBtn = document.getElementById('ai-cursor-toggle-btn');

        if (statusElement) {
          statusElement.textContent = status.enabled ? '已启用' : '已禁用';
          statusElement.className = `ai-status ${status.enabled ? 'ai-status--active' : 'ai-status--inactive'}`;
        }

        if (toggleBtn) {
          toggleBtn.textContent = status.enabled ? '禁用增强' : '启用增强';
          toggleBtn.className = `ai-btn ${status.enabled ? 'ai-btn--danger' : 'ai-btn--primary'}`;
        }

        // 更新功能特性显示
        if (status.features) {
          Object.entries(status.features).forEach(([feature, enabled]) => {
            const featureElement = document.getElementById(`ai-cursor-feature-${feature}`);
            if (featureElement) {
              featureElement.className = `ai-feature-status ${enabled ? 'ai-feature--enabled' : 'ai-feature--disabled'}`;
            }
          });
        }

        console.log('光标增强状态已更新:', status);
      }

    } catch (error) {
      console.error('加载光标增强状态失败:', error);
      this.showNotification('加载光标增强状态失败', 'error');
    }
  }

  // #endregion

  // #region 第三阶段模块初始化方法
  /**
   * @function initializeSettingsManager - 初始化设置管理器
   * @description 通过Background Service Worker初始化设置管理器
   * @returns {Promise<void>}
   */
  async initializeSettingsManager() {
    try {
      console.log('🔧 初始化设置管理器...');
      
      const response = await this.sendToBackground({
        type: 'ai:settings:get',
        data: { action: 'initialize' }
      });
      
      if (response && response.success) {
        this.moduleStates.settings = { enabled: true, initialized: true };
        console.log('✅ 设置管理器初始化完成');
      } else {
        throw new Error(response?.error || '设置管理器初始化失败');
      }
    } catch (error) {
      console.error('❌ 设置管理器初始化失败:', error);
      this.moduleStates.settings = { enabled: false, initialized: false };
      // 不抛出错误，允许其他模块继续初始化
    }
  }

  /**
   * @function initializeNotionConnector - 初始化Notion连接器
   * @description 通过Background Service Worker初始化Notion连接器
   * @returns {Promise<void>}
   */
  async initializeNotionConnector() {
    try {
      console.log('🔧 初始化Notion连接器...');
      
      const response = await this.sendToBackground({
        type: 'ai:notion:connect',
        data: { action: 'initialize' }
      });
      
      if (response && response.success) {
        this.moduleStates.notion = { enabled: true, initialized: true };
        console.log('✅ Notion连接器初始化完成');
      } else {
        console.warn('⚠️ Notion连接器初始化失败，但这是可选功能');
        this.moduleStates.notion = { enabled: false, initialized: false };
      }
    } catch (error) {
      console.error('❌ Notion连接器初始化失败:', error);
      this.moduleStates.notion = { enabled: false, initialized: false };
      // 不抛出错误，Notion是可选功能
    }
  }

  /**
   * @function onNotionTabActivated - Notion标签页激活回调
   * @description 处理Notion标签页激活时的逻辑
   */
  onNotionTabActivated() {
    console.log('🔄 Notion标签页已激活');

    // 更新Notion连接状态显示
    this.updateNotionStatus();

    // 如果已连接，刷新数据
    if (this.moduleStates.notion?.connected) {
      this.refreshNotionData();
    }
  }

  /**
   * @function connectNotion - 连接Notion
   * @description 启动Notion连接流程
   */
  async connectNotion() {
    console.log('发起Notion连接...');
    this.showLoading();
    this.sendToBackground({ type: MESSAGE_TYPES.NOTION_CONNECT });
  }

  /**
   * @function syncNotion - 同步到Notion
   * @description 将当前对话历史同步到Notion
   */
  async syncNotion() {
    console.log('手动触发Notion同步...');
    this.updateNotionStatus('syncing');
    this.sendToBackground({ type: MESSAGE_TYPES.NOTION_SYNC, payload: { force: true } });
  }

  /**
   * @function refreshNotionData - 刷新Notion数据
   * @description 刷新Notion相关数据显示和缓存
   */
  async refreshNotionData() {
    try {
      if (!this.moduleStates.notion?.connected) {
        return;
      }

      console.log('🔄 刷新Notion数据...');

      // 更新缓存
      const response = await this.sendToBackground({
        type: 'ai:notion:cache:update',
        data: { forceUpdate: true }
      });

      if (response && response.success) {
        console.log('✅ Notion缓存更新成功');
        this.showNotification(`缓存更新完成: ${response.totalPages} 个页面`, 'success');
        this.updateNotionStatus('connected', {
          cacheInfo: `缓存: ${response.totalPages} 页面`
        });
      } else {
        console.warn('⚠️ Notion缓存更新失败:', response?.error);
        this.updateNotionStatus('connected');
      }

    } catch (error) {
      console.error('❌ 刷新Notion数据失败:', error);
      this.showNotification('刷新失败: ' + error.message, 'error');
    }
  }

  /**
   * @function clearNotionCache - 清除Notion缓存
   * @description 清除本地Notion缓存
   */
  async clearNotionCache() {
    try {
      console.log('🗑️ 清除Notion缓存...');

      const response = await this.sendToBackground({
        type: 'ai:notion:cache:clear',
        data: {}
      });

      if (response && response.success) {
        console.log('✅ Notion缓存清除成功');
        this.showNotification('缓存已清除', 'success');
        this.updateNotionStatus('connected', { cacheInfo: '缓存已清除' });
      } else {
        console.error('❌ Notion缓存清除失败:', response?.error);
        this.showNotification('缓存清除失败: ' + (response?.error || '未知错误'), 'error');
      }

    } catch (error) {
      console.error('❌ 清除Notion缓存异常:', error);
      this.showNotification('缓存清除异常: ' + error.message, 'error');
    }
  }

  /**
   * @function getNotionCacheStatus - 获取Notion缓存状态
   * @description 获取当前Notion缓存的状态信息
   */
  async getNotionCacheStatus() {
    try {
      const response = await this.sendToBackground({
        type: 'ai:notion:cache:status',
        data: {}
      });

      if (response && response.success) {
        return response.status;
      } else {
        console.warn('⚠️ 获取缓存状态失败:', response?.error);
        return null;
      }

    } catch (error) {
      console.error('❌ 获取缓存状态异常:', error);
      return null;
    }
  }

  /**
   * @function updateNotionStatus - 更新Notion状态显示
   * @description 更新Notion连接状态的UI显示
   * @param {string} status - 状态 ('disconnected', 'connecting', 'connected', 'error')
   * @param {Object} data - 额外数据
   */
  updateNotionStatus(status = 'disconnected', data = {}) {
    const statusCard = document.getElementById('ai-notion-status-card');
    if (!statusCard) return;

    const iconElement = statusCard.querySelector('.ai-status-card__icon .ai-icon');
    const titleElement = statusCard.querySelector('.ai-status-card__title');
    const textElement = statusCard.querySelector('.ai-status-card__text');

    switch (status) {
      case 'connecting':
        iconElement.textContent = '🔄';
        titleElement.textContent = '连接中...';
        textElement.textContent = '正在连接到Notion，请稍候';
        break;
      case 'connected':
        iconElement.textContent = '✅';
        titleElement.textContent = '已连接';
        textElement.textContent = `已连接到Notion工作区${data.workspace ? ': ' + data.workspace : ''}`;
        break;
      case 'error':
        iconElement.textContent = '❌';
        titleElement.textContent = '连接失败';
        textElement.textContent = data.error || '连接Notion时发生错误';
        break;
      default: // disconnected
        iconElement.textContent = '❌';
        titleElement.textContent = '未连接';
        textElement.textContent = '点击"连接Notion"开始使用云端同步功能';
        break;
    }
  }

  /**
   * @function configureNotionToken - 配置Notion Integration Token
   * @description 通过UI配置Notion Integration Token
   * @param {string} token - Integration Token
   */
  async configureNotionToken(token) {
    try {
      if (!token || typeof token !== 'string' || token.trim() === '') {
        throw new Error('请提供有效的Integration Token');
      }

      console.log('🔧 配置Notion Integration Token...');

      // 保存Token到Chrome存储
      await chrome.storage.sync.set({ notion_integration_token: token.trim() });

      // 测试连接
      const response = await this.sendToBackground({
        type: 'ai:notion:connect',
        data: {}
      });

      if (response && response.success) {
        this.showNotification('Notion Integration Token配置成功', 'success');
        this.updateNotionStatus('connected', response);
        return true;
      } else {
        throw new Error(response?.error || 'Token验证失败');
      }

    } catch (error) {
      console.error('❌ 配置Notion Token失败:', error);
      this.showNotification('Token配置失败: ' + error.message, 'error');
      return false;
    }
  }

  /**
   * @function setupNotionTokenInput - 设置Notion Token输入处理
   * @description 为设置面板中的Notion Token输入框设置事件处理
   */
  setupNotionTokenInput() {
    const tokenInput = document.getElementById('ai-setting-notion-token');
    const toggleBtn = document.getElementById('ai-notion-token-toggle');

    if (tokenInput && toggleBtn) {
      // 切换密码显示/隐藏
      toggleBtn.addEventListener('click', () => {
        const isPassword = tokenInput.type === 'password';
        tokenInput.type = isPassword ? 'text' : 'password';
        toggleBtn.querySelector('.ai-icon').textContent = isPassword ? '🙈' : '👁️';
      });

      // Token输入变化时自动保存和验证
      let saveTimeout;
      tokenInput.addEventListener('input', () => {
        clearTimeout(saveTimeout);
        saveTimeout = setTimeout(async () => {
          const token = tokenInput.value.trim();
          if (token && token.startsWith('secret_') && token.length > 20) {
            await this.configureNotionToken(token);
          }
        }, 1000); // 1秒延迟自动保存
      });

      // 加载现有Token
      chrome.storage.sync.get(['notion_integration_token']).then(result => {
        if (result.notion_integration_token) {
          tokenInput.value = result.notion_integration_token;
        }
      });
    }
  }

  /**
   * @function initializeAdvancedAnalyzer - 初始化高级分析器
   * @description 通过Background Service Worker初始化高级分析器
   * @returns {Promise<void>}
   */
  async initializeAdvancedAnalyzer() {
    try {
      console.log('🔧 初始化高级分析器...');
      
      const response = await this.sendToBackground({
        type: 'ai:analysis:compare',
        data: { action: 'initialize' }
      });
      
      if (response && response.success) {
        this.moduleStates.analysis = { enabled: true, initialized: true };
        console.log('✅ 高级分析器初始化完成');
      } else {
        throw new Error(response?.error || '高级分析器初始化失败');
      }
    } catch (error) {
      console.error('❌ 高级分析器初始化失败:', error);
      this.moduleStates.analysis = { enabled: false, initialized: false };
      // 不抛出错误，允许其他模块继续初始化
    }
  }

  /**
   * @function initializeCursorEnhancer - 初始化光标增强器
   * @description 通过Background Service Worker初始化光标增强器
   * @returns {Promise<void>}
   */
  async initializeCursorEnhancer() {
    try {
      console.log('🔧 初始化光标增强器...');
      
      const response = await this.sendToBackground({
        type: 'ai:cursor:enhance',
        data: { action: 'initialize' }
      });
      
      if (response && response.success) {
        this.moduleStates.cursor = { enabled: true, initialized: true };
        console.log('✅ 光标增强器初始化完成');
        
        // 加载光标增强状态
        await this.loadCursorEnhancementStatus();
      } else {
        console.warn('⚠️ 光标增强器初始化失败，但这是可选功能');
        this.moduleStates.cursor = { enabled: false, initialized: false };
      }
    } catch (error) {
      console.error('❌ 光标增强器初始化失败:', error);
      this.moduleStates.cursor = { enabled: false, initialized: false };
      // 不抛出错误，光标增强是可选功能
    }
  }
  // #endregion

  // BEGIN_EDIT
  /**
   * @function handleGlobalKeydown - 全局快捷键处理
   * @param {KeyboardEvent} event - 键盘事件
   */
  handleGlobalKeydown(event) {
    // 示例：Esc 关闭模态框 / Ctrl+Shift+L 清空消息
    if (event.key === 'Escape') {
      this.hideSettingsModal?.();
    }
  }

  /**
   * @function handleVisibilityChange - 处理页面可见性变化
   */
  handleVisibilityChange() {
    if (document.visibilityState === 'visible') {
      this.updateStatusBar('页面已激活');
    } else {
      this.updateStatusBar('页面已隐藏');
    }
  }

  /**
   * @function toggleNotifications - 切换通知面板显示状态
   */
  toggleNotifications() {
    const panel = document.getElementById('ai-notifications-panel');
    if (panel) {
      panel.classList.toggle('hidden');
      if (!panel.classList.contains('hidden')) {
        // 清空角标
        this.notificationBadge.textContent = '0';
        this.notificationBadge.classList.add('hidden');
      }
    }
  }

  /**
   * @function exportAnalysisResults - 导出分析结果（占位）
   */
  exportAnalysisResults() {
    this.showNotification('导出功能暂未实现，敬请期待', 'info');
  }

  /**
   * @function onChatTabActivated - Chat 标签激活回调（占位）
   */
  onChatTabActivated() {
    // 目前仅确保输入框聚焦
    this.chatInput?.focus();
  }

  /**
   * @function onSettingsTabActivated - Settings 标签激活回调（占位）
   */
  onSettingsTabActivated() {
    // 加载当前设置（若尚未加载）
    if (!this.moduleStates.settings.loaded) {
      this.loadCurrentSettings?.();
      this.moduleStates.settings.loaded = true;
    }
  }
  // END_EDIT

  /**
   * @function displayAnalysisError - 显示分析错误信息
   * @param {string} message - 错误信息
   * @description 当分析流程出现错误时统一处理
   */
  displayAnalysisError(message) {
    // 使用统一通知栏显示错误，并更新状态指示器
    this.showNotification(message, 'error');
    this.updateStatusIndicator(message);
  }
}

// 等待DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  window.aiSidebarPanel = new AiSidebarPanel();
}); 